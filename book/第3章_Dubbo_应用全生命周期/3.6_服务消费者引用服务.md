## 服务消费者引用服务
### 编写和运行服务调用者
在Dubbo的微服务发现模型中提供者进行服务的导出与服务注册，消费者则根据提供者导出的服务进行服务订阅和服务引用，前面已经详细的说明了服务导出与服务注册，微服务发现模型的前半部分已经详细说明过了，接下来就通过服务消费者启动时候的生命周期来看下消费者是如何进行服务订阅与服务引用的，，回到服务消费者的示例中，接下来我们只看下相关的代码：

```java
public class ConsumerApplication {
    public static void main(String[] args) throws IOException {
        //构建服务引用配置
        ReferenceConfig<GreetingsService> reference = ReferenceBuilder.<GreetingsService>newBuilder()
                        .interfaceClass(GreetingsService.class)
                        .build();
        //获取启动类实例
        DubboBootstrap.getInstance()
        //构建应用配置
        .application(ApplicationBuilder.newBuilder().name("dubbo-demo-consumer").qosPort(22223).logger("slf4j").build())
        //构建注册中心配置 
       .registry(RegistryBuilder.newBuilder().address("zookeeper://127.0.0.1:2181").build())
                .reference(reference)
        .start();
        ...
    }
}
```

### 服务调用者应用启动原理
服务消费者同服务提供者一样通过调用<font style="color:#080808;background-color:#ffffff;">DubboBootstrap来封装配置，然后调用start方法来启动。在start方法中与提供者一样同样先调用DefaultApplicationDeployer的start方法来进行应用级的初始化和模块发布器的启动，在DefaultModuleDeployer中进行服务提供者启动、消费者引用和服务注册逻辑。</font>

**<font style="color:rgb(51, 51, 51);">DefaultModuleDeployer的startSync方法</font>**

```java
private synchronized Future startSync() throws IllegalStateException {
    ...
    //提供者导出服务
    exportServices(); 
    ...
    //消费者引用服务
    referServices(); 
    ...
    //注册服务
    registerServices();  
    ...  
}
```

前面已经通过提供者看了导出服务、注册服务，接下来就来看下消费者引用服务的逻辑

**<font style="color:rgb(51, 51, 51);">DefaultModuleDeployer的referServices方法</font>**遍历所有服务引用配置，依次引用服务，代码如下所示：

```java
private void referServices() {
    configManager.getReferences().forEach(rc -> {
        ...
        //SimpleReferenceCache类型用于缓存服务引用配置
        referenceCache.get(rc, false);  
         ...
    }
}
```

前面在配置消费者引用配置的时候消费者的配置将会添加到Dubbo管理器中这里获取到所有消费者配置后进行遍历依次引用。

然后就是单个服务的依次引用，在Dubbo中单个服务引用是一个逻辑相对复杂又工作繁重的事情，需要进行服务代理对象的创建，服务的订阅，服务的连接等等操作，这些操作只需要第一次进行服务引用时候执行，后续则直接从缓存中获取代理对象即可，Reference引用相关的逻辑整体可以参考下图：

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1736689639942-b04b3e5c-850a-4036-96d3-3e97f02bb946.png)

关于缓存的逻辑则位于SimpleReferenceCache的get方法中，如下代码所示：

```java
public <T> T get(ReferenceConfigBase<T> rc, boolean check) {
        ...
        T proxy = null;
        // 默认为null 从引用缓存ConcurrentMap中获取
        if (singleton) {
            proxy = get(key, (Class<T>) type);
        } 
        ...
        //第一次缓存中为空开始创建引用
        if (proxy == null) {
            ...
            proxy = rc.get(check);
        }
        return proxy;
    }
```

<font style="color:#080808;background-color:#ffffff;">如果是第一次则将调用ReferenceConfig的get方法来进行引用服务获取代理对象，同样如果需要手动进行服务引用获取代理对象的时候也是可以执行ReferenceConfig的get方法来进行服务引用获取代理对象进行调用</font>

```java
public T get(boolean check) {
    ...
    if (ref == null) {
        ...
        //初始化服务引用
        //check参数为代理对象创建后是否检查提供端对象invoker是否可用
        init(check);
    }
    return ref;
}
```

 <font style="color:#080808;background-color:#ffffff;">ReferenceConfig的init方法比较简单主要逻辑是调用createProxy方法创建代理对象，如下代码所示：</font>

```java
protected void init(boolean check) {
    ...
    ref = createProxy(referenceParameters);
    ...
}
```

 <font style="color:#080808;background-color:#ffffff;">接下来就进入创建代理对象的核心代码ReferenceConfig的createProxy方法：</font>

```plain
private T createProxy(Map<String, String> referenceParameters) {
    ...
    //调用器Invoker对象创建
    createInvoker();
    ... 
    //消费者发布服务元数据
    MetadataUtils.publishServiceDefinition(consumerUrl, consumerModel.getServiceModel(), getApplicationModel());

    //为当前服务创建代理对象
    return (T) proxyFactory.getProxy(invoker, ProtocolUtils.isGeneric(generic));
}
```

### 消费者调用器Invoker对象创建
```java
private void createInvoker() {
    ...
    //protocolSPI为协议自适应类Protocol$Adaptive
    invoker = protocolSPI.refer(interfaceClass, curUrl);
    ...
}
```

 前面了解过服务提供者进行协议自适应类Protocol$Adaptive导出提供者的过程，这里使用自适应类型进行服务接口的引用，服务引用同服务提供一样根据协议名字获取对应扩展，在服务引用中通过调用refer方法进行引用，服务引用和服务导出相同的地方是都是通过自适应类开始调用协议包装类的责任链方法最后调用到RegistryProtocol中的方法，关于协议执行链路可以参考前面章节：《protocolSPI导出服务的执行链》 ，首先来整体看下引用服务到迁移Invoker创建的过程，如下图所示：

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1736725959107-d44f2beb-54cb-4b45-8a1a-329e74174390.png)

接下来我们先定位到RegistryProtocol的服务引用方法来看下服务引用和服务导出不同的地方。

```java
@Override
public <T> Invoker<T> refer(Class<T> type, URL url) throws RpcException {
    ...
    return doRefer(cluster, registry, type, url, qs);
}
```

执行服务引用时候的URL为注册中心配置，将会从注册中心获取服务信息，URL值如下所示：

> zookeeper://127.0.0.1:2181/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default&application=dubbo-demo-consumer&dubbo=2.0.2&environment=product&executor-management-mode=isolation&file-cache=true&logger=slf4j&pid=36559&qos.port=22223&release=3.3.2&timestamp=1736690727976
>

<font style="color:#080808;background-color:#ffffff;">在</font>RegistryProtocol的doRefer方法中，先获取具备迁移功能的<font style="color:#080808;background-color:#ffffff;">MigrationInvoker对象，然后将在interceptInvoker方法中根据接口级或者应用级的迁移配置来创建接口级和应用级的Invoker对象如下代码所示：</font>

```java
protected <T> Invoker<T> doRefer(
    Cluster cluster, Registry registry, Class<T> type, URL url, Map<String, String> parameters) {
    ...
    //获取具备接口级到应用级迁移能力的Invoker调用器对象MigrationInvoker
    ClusterInvoker<T> migrationInvoker = getMigrationInvoker(this, cluster, registry, type, url, consumerUrl);
    return interceptInvoker(migrationInvoker, url, consumerUrl);
}
```

 RegistryProtocol的interceptInvoker方法如下所示

```java
protected <T> Invoker<T> interceptInvoker(ClusterInvoker<T> invoker, URL url, URL consumerUrl) {
    //MigrationRuleListener
    List<RegistryProtocolListener> listeners = findRegistryProtocolListeners(url);
    ...
    for (RegistryProtocolListener listener : listeners) {
        listener.onRefer(this, invoker, consumerUrl, url);
    }
    return invoker;
}
```

 在RegistryProtocol中默认只有一个RegistryProtocolListener类型为<font style="color:#080808;background-color:#ffffff;">MigrationRuleListener，MigrationRuleListener 类型的作用是监听配置中心的迁移规则，并根据这些规则动态调整服务的调用行为。主要要用于 Dubbo 服务的消费者端，确保在服务迁移过程中能够平滑过渡。</font>

MigrationRuleListener的onRefer方法如下所示：

```java
@Override
public void onRefer(
        RegistryProtocol registryProtocol, ClusterInvoker<?> invoker, URL consumerUrl, URL registryURL) {
    //初始化迁移规则处理器 MigrationRuleHandler
    MigrationRuleHandler<?> migrationRuleHandler =
            ConcurrentHashMapUtils.computeIfAbsent(handlers, (MigrationInvoker<?>) invoker, _key -> {
                ((MigrationInvoker<?>) invoker).setMigrationRuleListener(this);
                return new MigrationRuleHandler<>((MigrationInvoker<?>) invoker, consumerUrl);
            });

    migrationRuleHandler.doMigrate(rule);
}
```

 在MigrationRuleHandler类型的doMigrate和refreshInvoker方法中开始根据迁移规则来执行接口级到应用级的迁移逻辑，<font style="color:rgb(33, 37, 41);">当前共存在三种状态：</font>

+ <font style="color:rgb(33, 37, 41);">APPLICATION_FIRST（应用级优先默认）</font>
+ <font style="color:rgb(33, 37, 41);">FORCE_INTERFACE（强制接口级）</font>
+ <font style="color:rgb(33, 37, 41);">FORCE_APPLICATION（强制应用级）</font>

<font style="color:rgb(33, 37, 41);">APPLICATION_FIRST：开启接口级、应用级双订阅，运行时根据阈值和灰度流量比例动态决定调用流量走向 。</font>

<font style="color:rgb(33, 37, 41);">FORCE_INTERFACE：只启用兼容模式下接口级服务发现的注册中心逻辑，调用流量 100% 走原有流程 。</font>

<font style="color:rgb(33, 37, 41);">FORCE_APPLICATION：只启用新模式下应用级服务发现的注册中心逻辑，调用流量 100% 走应用级订阅的地址。</font>

<font style="color:rgb(33, 37, 41);">整体逻辑过程如下图所示：</font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1736726110272-57daffca-c7de-4511-9d41-97e727cc7dc4.png)

MigrationRuleHandler类型的doMigrate方法如下所示：

```java
public void doMigrate(MigrationRule rule) {
    //消费端迁移模式一共有三种：
    //1） 应用级优先APPLICATION_FIRST（默认的）
    //2） 强制接口级FORCE_INTERFACE
    //3） 强制应用级FORCE_APPLICATION
    MigrationStep step = MigrationStep.APPLICATION_FIRST;
    ...
    //省略了迁移threshold和rule处理逻辑，这里开始刷新Invoker
    refreshInvoker(step, threshold, rule))
     
}
```

MigrationRuleHandler类型的refreshInvoker方法

```java
private boolean refreshInvoker(MigrationStep step, Float threshold, MigrationRule newRule) {
    ...
    boolean success = true;
    switch (step) {
        case APPLICATION_FIRST:
            migrationInvoker.migrateToApplicationFirstInvoker(newRule);
            break;
        case FORCE_APPLICATION:
            success = migrationInvoker.migrateToForceApplicationInvoker(newRule);
            break;
        case FORCE_INTERFACE:
        default:
            success = migrationInvoker.migrateToForceInterfaceInvoker(newRule);
    }
    ...
}
```

<font style="color:#080808;background-color:#ffffff;">MigrationInvoker的migrateToApplicationFirstInvoker方法主要执行三个逻辑，创建接口级调用器Invoker对象，创建应用级调用器Invoker对象，根据迁移规则配置来选择使用接口级或者应用级的Invoker对象如下代码所示</font>

```java
@Override
public void migrateToApplicationFirstInvoker(MigrationRule newRule) {
    //刷新接口级调用器
    refreshInterfaceInvoker(latch);
    //刷新应用级调用器
    refreshServiceDiscoveryInvoker(latch);
    // 根据迁移规则计算出当前首选的调用器（Invoker）
    calcPreferredInvoker(newRule);
}
```

### 接口级服务订阅Invoker对象创建
默认以应用级优先的策略创建Invoker对象的时候，接口级调用器Invoker和应用级Invoker对象都会创建，接下来可以看下接口级Invoker对象的创建过程如下图所示，接口级Invoker对象创建的时候会进行消费者信息的注册、路由链构建、服务订阅通知、集群Invoker调用器创建等等逻辑。

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1736729603322-327a71b5-01be-422d-8450-e3da9351e909.png)

下面就从入口<font style="color:#080808;background-color:#ffffff;">MigrationInvoker的refreshInterfaceInvoker方法看起，如下所示：</font>

```java
protected void refreshInterfaceInvoker(CountDownLatch latch) {
    ...
    invoker = registryProtocol.getInvoker(cluster, registry, type, url);
    ...
}
```

<font style="color:#080808;background-color:#ffffff;">InterfaceCompatibleRegistryProtocol的getInvoker方法</font>

```java
@Override
public <T> ClusterInvoker<T> getInvoker(Cluster cluster, Registry registry, Class<T> type, URL url) {
    DynamicDirectory<T> directory = new RegistryDirectory<>(type, url);
    return doCreateInvoker(directory, cluster, registry, type);
}
```

<font style="color:#080808;background-color:#ffffff;">Directory类型的作用是管理服务提供者的目录，负责维护和更新服务提供者列表，并根据负载均衡策略选择合适的服务提供者进行调用</font>

<font style="color:#080808;background-color:#ffffff;">RegistryDirectory层级关系如下：</font>

+ <font style="color:#080808;background-color:#ffffff;">AbstractDirectory（提供了一些通用的功能和属性，负责：保存消费者的URL信息。管理服务提供者的列表。提供服务提供者的选择逻辑）</font>
+ <font style="color:#080808;background-color:#ffffff;">DynamicDirectory（动态管理服务提供者列表。它会根据注册中心的通知动态更新服务提供者列表）</font>
+ <font style="color:#080808;background-color:#ffffff;">RegistryDirectory（处理注册中心的服务目录。它会根据注册中心的变化动态更新服务提供者列表，并提供负载均衡和路由功能）</font>

<font style="color:#080808;background-color:#ffffff;">RegistryProtocol的doCreateInvoker包含了接口级Invoker对象创建的核心逻辑，主要包含如下内容：</font>

+ 消费者服务信息注册
+ 流量路由链构建
+ 消费者订阅服务
+ 创建集群调用器

```java
protected <T> ClusterInvoker<T> doCreateInvoker(
    DynamicDirectory<T> directory, Cluster cluster, Registry registry, Class<T> type) { 
    ...
    //消费者服务注册
    registry.register(directory.getRegisteredConsumerUrl());
    //消费者路由执行链构建
    directory.buildRouterChain(urlToRegistry);
    //消费者订阅服务
    directory.subscribe(toSubscribeUrl(urlToRegistry));
    //创建集群调用器
    return (ClusterInvoker<T>) cluster.join(directory, true);
}
```

<font style="color:#080808;background-color:#ffffff;"> 消费者服务注册，在最开始看到的服务发现模型中，并没有提到消费者的注册，Dubbo中将消费者信息注册在注册中心好处就是方便了后期的针对消费者的运维管控，坏处就是增加了注册中心的负担。</font>

<font style="color:#080808;background-color:#ffffff;">下面按照执行顺序列举：</font>

+ <font style="color:#080808;background-color:#ffffff;">ListenerRegistryWrapper</font>
+ <font style="color:#080808;background-color:#ffffff;">AbstractRegistry (内存缓存已注册的消费者）</font>
+ <font style="color:#080808;background-color:#ffffff;">FailbackRegistry（向注册中心注册，具备重试能力）</font>
+ <font style="color:#080808;background-color:#ffffff;">ZookeeperRegistry （与Zookeeper的交互）</font>

中间过程可以忽略直接从<font style="color:#080808;background-color:#ffffff;">FailbackRegistry调用注册实现的doRegister方法开始，如下所示：</font>

```java
@Override
public void register(URL url) {
    ...
    //向服务器端发送注册请求
    doRegister(url);
    ...
}
```

<font style="color:#080808;background-color:#ffffff;"> 接下来调用ZookeeperRegistry类型的doRegister方法，在doRegister方法中调用zkClient向Zookeeper注册中心中注册临时节点，如下代码所示：</font>

```java
@Override
public void doRegister(URL url) {
    ...
    //消费者临时节点
    zkClient.create(toUrlPath(url), url.getParameter(DYNAMIC_KEY, true), true);
    ...
}
```

<font style="color:#080808;background-color:#ffffff;">u消费者注册的URL信息如下所示：</font>

> <font style="color:#080808;background-color:#ffffff;">/dubbo/org.demo.app.GreetingsService/consumers/consumer://172.20.91.202/org.demo.app.GreetingsService?application=dubbo-demo-consumer&background=false&category=consumers&check=false&dubbo=2.0.2&environment=product&executor-management-mode=isolation&file-cache=true&interface=org.demo.app.GreetingsService&logger=slf4j&methods=sayHi&pid=37997&qos.port=22223&release=3.3.2&side=consumer&sticky=false&timestamp=1736729328560&unloadClusterRelated=false</font>
>

<font style="color:#080808;background-color:#ffffff;">消费者服务信息注册完成之后则开始进行流量路由的责任链构建，</font><font style="color:rgb(33, 37, 41);">Dubbo 提供了丰富的流量管控策略，</font>**<font style="color:rgb(33, 37, 41);">基于路由规则的流量管控</font>**<font style="color:rgb(33, 37, 41);">，路由规则对每次请求进行条件匹配，并将符合条件的请求路由到特定的地址子集。</font>

<font style="color:rgb(33, 37, 41);">Dubbo 的流量管控规则可以基于应用、服务、方法、参数等粒度精准的控制流量走向，根据请求的目标服务、方法以及请求体中的其他附加参数进行匹配，符合匹配条件的流量会进一步的按照特定规则转发到一个地址子集。流量管控规则有以下几种：</font>

+ <font style="color:rgb(33, 37, 41);">条件路由规则</font>
+ <font style="color:rgb(33, 37, 41);">标签路由规则</font>
+ <font style="color:rgb(33, 37, 41);">脚本路由规则</font>
+ <font style="color:rgb(33, 37, 41);">动态配置规则</font>

<font style="color:rgb(33, 37, 41);">以下是 Dubbo 单个路由器的工作过程，路由器接收一个服务的实例地址集合作为输入，基于请求上下文 (Request Context) 和 (Router Rule) 实际的路由规则定义对输入地址进行匹配，所有匹配成功的实例组成一个地址子集，最终地址子集作为输出结果继续交给下一个路由器或者负载均衡组件处理。</font>

<font style="color:rgb(33, 37, 41);">  
</font>![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1736743253566-f59b390e-f3c5-4aa6-9025-f490634f5e33.png)

如果有多个路由规则则形成路由链如下所示：

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1736743323906-8e863b1a-9555-46f6-b293-f70560361ad0.png)

接下来就来简单看下消费者的路由链是如何构建完成的

<font style="color:#080808;background-color:#ffffff;">DynamicDirectory中调用buildRouterChain方法来构建路由链，如下代码：</font>

```java
public void buildRouterChain(URL url) {
    this.setRouterChain(RouterChain.buildChain(getInterface(), url));
}
```

<font style="color:#080808;background-color:#ffffff;"> </font>

<font style="color:#080808;background-color:#ffffff;">buildRouterChain方法中调用RouterChain的buildChain方法来构建。在 buildChain 方法中调用两次 buildSingleChain 方法是为了创建两个 SingleRouterChain 实例：mainChain 和 backupChain。这样做的目的是为了在路由链更新时提供一种机制，以确保在更新过程中不会中断服务调用。</font>

```plain
public static <T> RouterChain<T> buildChain(Class<T> interfaceClass, URL url) {
    SingleRouterChain<T> chain1 = buildSingleChain(interfaceClass, url);
    SingleRouterChain<T> chain2 = buildSingleChain(interfaceClass, url);
    return new RouterChain<>(new SingleRouterChain[] {chain1, chain2});
}
```

<font style="color:#080808;background-color:#ffffff;"></font>

<font style="color:#080808;background-color:#ffffff;">RouterChain的buildSingleChain方法 </font>

```java
public static <T> SingleRouterChain<T> buildSingleChain(Class<T> interfaceClass, URL url) {
    ModuleModel moduleModel = url.getOrDefaultModuleModel();
    ...
    //状态路由目前有7个
    List<StateRouter<T>> stateRouters =
            moduleModel.getExtensionLoader(StateRouterFactory.class).getActivateExtension(url, ROUTER_KEY).stream()
                    .map(factory -> factory.getRouter(interfaceClass, url))
                    .collect(Collectors.toList());

    ...
    return new SingleRouterChain<>(routers, stateRouters, shouldFailFast, routerSnapshotSwitcher);
}
```

<font style="color:#080808;background-color:#ffffff;"> </font>

<font style="color:rgb(33, 37, 41);">Dubbo 中提供了 Router 这个可以动态进行选址路由的能力，同时绝大多数的服务治理能力也都是基于这个 Router 扩展点实现的。在 Dubbo 3.x 中，Dubbo 在 Router 的基础上抽象了全新的 StateRouter 机制，可以在选址性能以及内存占用上有大幅优化。</font>

<font style="color:rgb(77, 77, 77);">普通Router默认为空需要根据预设的路由规则进行路由决策。</font>

<font style="color:#080808;background-color:#ffffff;">通过StateRouterFactory扩展获取的 </font><font style="color:rgb(33, 37, 41);">StateRouter</font><font style="color:#080808;background-color:#ffffff;">状态路由则有如下几种：</font>

| <font style="background-color:#D9EAFC;">类型</font> | <font style="background-color:#D9EAFC;">说明</font> |
| --- | --- |
| <font style="color:#080808;background-color:#ffffff;">MockInvokersSelector</font> | <font style="color:#080808;background-color:#ffffff;">用于处理服务调用的 Mock 逻辑</font> |
| <font style="color:#080808;background-color:#ffffff;">StandardMeshRuleRouter</font> | <font style="color:#080808;background-color:#ffffff;">服务网格的规则进行流量路由</font> |
| <font style="color:#080808;background-color:#ffffff;">TagStateRouter</font> | 标签进行流量路由 |
| <font style="color:#080808;background-color:#ffffff;">ServiceStateRouter</font> | 基于服务状态进行流量路由 |
| <font style="color:#080808;background-color:#ffffff;">ProviderAppStateRouter</font> | 基于服务提供者应用的状态进行流量路由 |
| <font style="color:#080808;background-color:#ffffff;">AppStateRouter</font> | 基于应用状态进行流量路由 |
| <font style="color:#080808;background-color:#ffffff;">AppScriptStateRouter</font> | 基于应用脚本状态进行流量路由 |
| <font style="color:#080808;background-color:#ffffff;">TailStateRouter</font> | 尾部路由器，处理最终的路由逻辑 |


<font style="color:#080808;background-color:#ffffff;"> 路由执行链路的构建SingleRouterChain构造器</font>

```java
 public SingleRouterChain(
            List<Router> routers,
            List<StateRouter<T>> stateRouters,
            boolean shouldFailFast,
            RouterSnapshotSwitcher routerSnapshotSwitcher) {
        //初始化普通路由
        initWithRouters(routers);、
        //初始化有状态路由
        initWithStateRouters(stateRouters);
    }

```

<font style="color:#080808;background-color:#ffffff;"> </font>

<font style="color:#080808;background-color:#ffffff;">SingleRouterChain的initWithStateRouters方法初始化状态路由器链，将状态路由器按顺序链接起来，构建路由链路如下代码所示。</font>

```java
private void initWithStateRouters(List<StateRouter<T>> stateRouters) {
        StateRouter<T> stateRouter = TailStateRouter.getInstance();
        for (int i = stateRouters.size() - 1; i >= 0; i--) {
            StateRouter<T> nextStateRouter = stateRouters.get(i);
            //头插法 将新节点nextStateRouter插入stateRouter头部
            nextStateRouter.setNextRouter(stateRouter);
            //指向路由链表头部
            stateRouter = nextStateRouter;
        }
        this.headStateRouter = stateRouter;
        this.stateRouters = Collections.unmodifiableList(stateRouters);
    }
```

<font style="color:#080808;background-color:#ffffff;"> 前面了解了消费者注册服务信息和路由链的构建，接下来要进入服务发现模型中的消费者订阅服务的逻辑，服务订阅分为启动服务订阅和变更服务通知，这里主要以启动进程主动订阅服务来引入，先来通过流程图来整体看下服务订阅</font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1736953544436-ff89c65c-066b-4d66-9ca6-93e5f5fcf0b3.png)

<font style="color:#080808;background-color:#ffffff;">RegistryDirectory的subscribe方法，如下所示：</font>

```java
@Override
public void subscribe(URL url) {
    ...
    super.subscribe(url);
    ...
}
```

<font style="color:#080808;background-color:#ffffff;">RegistryDirectory注册目录在订阅服务的时候将会调用根据调用关系调用父类型的subscribe方法，然后调用注册中心的订阅方法，具体内容不做展开，如下所示：</font>

+ <font style="color:#080808;background-color:#ffffff;">DynamicDirectory的subscribe</font>
+ <font style="color:#080808;background-color:#ffffff;"> ListenerRegistryWrapper的subscribe</font>
+ <font style="color:#080808;background-color:#ffffff;">FailbackRegistry的subscribe 调用doSubscribe进行订阅，订阅异常则重试</font>
+ <font style="color:#080808;background-color:#ffffff;">AbstractRegistry的subscribe 缓存url到已订阅集合中subscribed</font>

<font style="color:#080808;background-color:#ffffff;"> 最后会调用到ZookeeperRegistry的doSubscribe实现，在这个方法中执行服务的订阅与通知逻辑：</font>

```java
@Override
public void doSubscribe(final URL url, final NotifyListener listener) {
    ...
    List<URL> urls = new ArrayList<>();
    /*
         迭代URL中的类别值。使用默认设置，当URL是消费者URL时，路径变量可以是             
         /dubbo/[service name]/providers,
         /dubbo/[service name]/configurators
         /dubbo/[service name]/routers
    */
    for (String path : toCategoriesPath(url)) {
        ...
        //创建提供者、配置、路由目录，为持久节点
        //dubbo/org.demo.app.GreetingsService/providers
        //dubbo/org.demo.app.GreetingsService/configurators
        //dubbo/org.demo.app.GreetingsService/routers
        zkClient.create(path, false, true);

        //监听路径 监听类型为：RegistryChildListenerImpl
        //用于在节点变更时候触发ZookeeperRegistryNotifier的notify方法来刷新数据
        List<String> children = zkClient.addChildListener(path, zkListener);
        //toUrlsWithEmpty确保在没有可用提供者或配置时，系统能够正确处理并生成相应的空 URL
        urls.addAll(toUrlsWithEmpty(url, path, children));
        ...
    }
    //首次订阅触发通知 刷新数据 这里RegistryDirectory为其中一个订阅者
    notify(url, listener, urls);
    ...
}
```

<font style="color:#080808;background-color:#ffffff;"> 订阅方法发生在消费者启动的时候，在订阅方法中消费者遍历提供者和配置路径，遍历的时候会判断注册中心是否存在对应路径如果不存在则创建路径，然后使用zkClient.addChildListener方法借助Zookeeper的Watch机制对节点进行监听，当节点数据发生了变更时则执行ZookeeperRegistryNotifier的notify方法来刷新数据，ZookeeperRegistryNotifier的notify方法在刷新数据的时候将会调用FailbackRegistry的notify方法来执行通知逻辑，无论是首次订阅还是后续数据刷新执行的都是FailbackRegistry的notify方法，接下来我们就来看下当消费者订阅到提供者或者配置信息刷新之后的逻辑。</font>

<font style="color:#080808;background-color:#ffffff;">notify方法的执行将会执行如下几个方法，</font>

+ <font style="color:#080808;background-color:#ffffff;">FailbackRegistry的notify方法</font>
+ <font style="color:#080808;background-color:#ffffff;">FailbackRegistry的doNotify方法</font>
+ <font style="color:#080808;background-color:#ffffff;">AbstractRegistry的notify方法</font>

<font style="color:#080808;background-color:#ffffff;">前面几个方法的执行逻辑比较简单直接省略，我们重点看AbstractRegistry的notify模版方法首先来列举下前面转换的 参数：</font>

+ <font style="color:#080808;background-color:#ffffff;">url参数</font>
    - <font style="color:#080808;background-color:#ffffff;">consumer://***********/org.demo.app.GreetingsService?application=dubbo-demo-consumer&background=false&category=providers,configurators,routers&dubbo=2.0.2&environment=product&executor-management-mode=isolation&file-cache=true&interface=org.demo.app.GreetingsService&logger=slf4j&methods=sayHi&pid=39880&qos.port=22223&release=3.3.2&side=consumer&sticky=false&timestamp=1736772225697&unloadClusterRelated=false</font>
+ <font style="color:#080808;background-color:#ffffff;">listener参数</font>
    - <font style="color:#080808;background-color:#ffffff;">RegistryDirectory类型对象</font>
+ <font style="color:#080808;background-color:#ffffff;">urls集合（提供者、配置、路由）：</font>
    - <font style="color:#080808;background-color:#ffffff;">dubbo://***********:20880/org.demo.app.GreetingsService?application=dubbo-demo-provider&background=false&category=providers,configurators,routers&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&environment=product&executor-management-mode=isolation&file-cache=true&generic=false&interface=org.demo.app.GreetingsService&ipv6=2409:8a1e:aba0:9100:747c:b2df:d396:e806&logger=slf4j&methods=sayHi&pid=39880&prefer.serialization=hessian2,fastjson2&qos.port=22223&release=3.3.2&service-name-mapping=true&side=provider&sticky=false&unloadClusterRelated=false</font>
    - <font style="color:#080808;background-color:#ffffff;">empty://***********/org.demo.app.GreetingsService?application=dubbo-demo-consumer&background=false&category=configurators&dubbo=2.0.2&environment=product&executor-management-mode=isolation&file-cache=true&interface=org.demo.app.GreetingsService&logger=slf4j&methods=sayHi&pid=39880&qos.port=22223&release=3.3.2&side=consumer&sticky=false&timestamp=1736772225697&unloadClusterRelated=false</font>
    - <font style="color:#080808;background-color:#ffffff;">empty://***********/org.demo.app.GreetingsService?application=dubbo-demo-consumer&background=false&category=routers&dubbo=2.0.2&environment=product&executor-management-mode=isolation&file-cache=true&interface=org.demo.app.GreetingsService&logger=slf4j&methods=sayHi&pid=39880&qos.port=22223&release=3.3.2&side=consumer&sticky=false&timestamp=1736772225697&unloadClusterRelated=false</font>

<font style="color:#080808;background-color:#ffffff;">notify方法的代码如下：</font>

```java
protected void notify(URL url, NotifyListener listener, List<URL> urls) {
        ...
        //参数urls将会经过处理后存入result容器
        //routers、configurators、providers
         for (Map.Entry<String, List<URL>> entry : result.entrySet()) {
            String category = entry.getKey();
            List<URL> categoryList = entry.getValue();
            categoryNotified.put(category, categoryList);
            //调用RegistryDirectory 用于缓存配置、路由，刷新invoker配置
            listener.notify(categoryList);

            // 我们将在每次通知后更新缓存文件，可以用于注册中心异常之后的降级处理。
            // 当我们的Registry由于网络抖动导致订阅失败时，我们至少可以返回现有的缓存 URL。
            if (localCacheEnabled) {
                saveProperties(url);
            }
        }
    }
```

<font style="color:#080808;background-color:#ffffff;">在AbstractRegistry的notify方法中将会根据需要通知的urls（提供者、配置、路由规则）进行遍历依次执行RegistryDirectory的notify方法，执行通知方法之后则会将数据缓存在本地磁盘一份，当注册中心异常的时候可以使用本地缓存的文件。</font>

```java
@Override
    public synchronized void notify(List<URL> urls) {
        ...
        //这里省略了配置处理和路由规则处理的代码
        //主要看提供者数据处理的逻辑，在notify方法中会将urls转换为providerURLs集合
        ...
        refreshOverrideAndInvoker(providerURLs);
    }
```

<font style="color:#080808;background-color:#ffffff;">在通知notify方法中将会调用  RegistryDirectory的refreshOverrideAndInvoker方法来刷新配置和Invoker对象，代码如下所示：</font>

```java
   @Override
    protected synchronized void refreshOverrideAndInvoker(List<URL> urls) {
        //根据配置 接口级为override协议来更新需要订阅的地址
        this.directoryUrl = overrideWithConfigurator(getOriginalConsumerUrl());
        refreshInvoker(urls);
    }
```

<font style="color:#080808;background-color:#ffffff;"> overrideWithConfigurator方法用于更新订阅到的提供者配置刷新，提供者配置有多种比如：</font>

+ <font style="color:#080808;background-color:#ffffff;">接口级override协议配置dubbo 2.6及以前就有的逻辑</font>
+ <font style="color:#080808;background-color:#ffffff;">应用级配置：app-name.configurators</font>
+ <font style="color:#080808;background-color:#ffffff;">接口级配置：service-name.configurators</font>

<font style="color:#080808;background-color:#ffffff;">RegistryDirectory的refreshInvoker方法将会把提供者url转换为Invoker，将 invokerURL 列表转换为 Invoker Map，转换的规则如下：</font>

+ <font style="color:#080808;background-color:#ffffff;">如果 URL 已转换为 invoker，则不再重新引用并直接从缓存中获取（请注意，URL 中的任何参数更改都将被重新引用）。</font>
+ <font style="color:#080808;background-color:#ffffff;">调用者列表不为空，则表示它是最新的调用者列表。</font>
+ <font style="color:#080808;background-color:#ffffff;">调用者列表为空，则表示该规则只是覆盖规则或路由规则，需要重新对比才能决定是否重新引用。</font>

```java
private void refreshInvoker(List<URL> invokerUrls) {
    ...
    //服务提供者全部关闭的时候禁止访问进行销毁
    if (invokerUrls.size() == 1
        && invokerUrls.get(0) != null
        && EMPTY_PROTOCOL.equals(invokerUrls.get(0).getProtocol())) {
         ...
         this.forbidden = true // Forbid to access
         ...
         destroyAllInvokers(); // Close all invokers
    } else {
        // Allow to access
        this.forbidden = false; 
        ...
        //推空保护，当推送空地址时候使用本地缓存的地址
        Set<URL> localCachedInvokerUrls = this.cachedInvokerUrls;
        if (invokerUrls.isEmpty()) {
            if (CollectionUtils.isNotEmpty(localCachedInvokerUrls)) {
                ...
                invokerUrls.addAll(localCachedInvokerUrls);
            }
        }
        ...
        //这个方法是将url转换为Invoker对象
        Map<URL, Invoker<T>> newUrlInvokerMap = toInvokers(oldUrlInvokerMap, invokerUrls);  
        ...
        //如果存在不使用的invoker则销毁已下线的提供者
        destroyUnusedInvokers(oldUrlInvokerMap, newUrlInvokerMap); // Close the unused Invoker
        ...
        //通知invokers刷新
        this.invokersChanged();
    }
    ...
}
```

<font style="color:#080808;background-color:#ffffff;"> 在RegistryDirectory的toInvokers方法中会先判断服务的url是否发生了变化，如果未发生变化则直接将缓存的invoker添加到结果中，如果url对应的缓存中不存在invoker对象则先进行服务启用/禁用状态的判断，如果服务处于启用状态则进行服务的引用，如下代码所示：</font>

```java
private Map<URL, Invoker<T>> toInvokers(Map<URL, Invoker<T>> oldUrlInvokerMap, List<URL> urls) {
    for (URL providerUrl : urls) {
        ...
        //获取当前缓存的url对应的invoker对象，
        //如果存在则将从oldUrlInvokerMap中移除这个invoker加入newUrlInvokerMap
        //注意：缓存key为url，如果服务 url 发生变化，将会重新执行服务引用
        Invoker<T> invoker = oldUrlInvokerMap == null ? null : oldUrlInvokerMap.remove(url);
        if (invoker == null) { // Not in the cache, refer again
            ...
            //！启用/禁用服务，通过此配置可以控制服务流量上下线
            boolean enabled = true;
            if (url.hasParameter(DISABLED_KEY)) {
                //url配置中通过disabled=true可以禁用服务
                enabled = !url.getParameter(DISABLED_KEY, false);
            } else {
                //默认disabled和enabled 都未配置走这里逻辑，
                //默认值为true，代表启用服务
                enabled = url.getParameter(ENABLED_KEY, true);
            }
            //启用服务进行服务引用
            if (enabled) {
                invoker = protocol.refer(serviceType, url);
            }
                ...
            //服务引用成功后invoker对象不为空
            if (invoker != null) { // Put new invoker in cache
                newUrlInvokerMap.put(url, invoker);
            }
        } else {
            //当前invoker已经存在则直接更新到newUrlInvokerMap
            newUrlInvokerMap.put(url, invoker);
        }
    }
    //返回处理后的invoker对象
    return newUrlInvokerMap;
}
```

在toInvokers方法中我们重点服务的启用/禁用配置，通过在url配置中通过disabled=true可以禁用服务，如果服务启用则开始引用服务，引用服务的过程将会创建与服务提供端的长连接，协议的<font style="color:#080808;background-color:#ffffff;">refer对象执行的时候会先经过wrapper机制进行逻辑切面处理，然后再调用DubboProtocol的refer方法进行引用服务，整体的引用服务创建网络客户端的逻辑如下图所示：</font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1736954471305-202b4e6c-1314-48ae-b99d-5f0fe4c26d0e.png)

<font style="color:#080808;background-color:#ffffff;">DubboProtocol的refer方法执行过程如下所示：</font>

+ <font style="color:#080808;background-color:#ffffff;">Protocol$Adaptive 用于动态适配不同的协议实现，根据 URL 中的协议参数选择具体的协议实现。</font>
+ ...协议Wrapper类型
+ <font style="color:#080808;background-color:#ffffff;">DubboProtocol Dubbo 默认的协议实现，负责处理 Dubbo 协议的具体逻辑</font>

<font style="color:#080808;background-color:#ffffff;">在前面章节我们了解了提供端DubboProtocol的export方法，这里来看下消费端中DubboProtocol的refer方法</font>

```java
  @Override
    public <T> Invoker<T> refer(Class<T> type, URL url) throws RpcException {
        ...
        return protocolBindingRefer(type, url);
    }
```

<font style="color:#080808;background-color:#ffffff;"> </font>

<font style="color:#080808;background-color:#ffffff;">DubboProtocol的protocolBindingRefer方法</font>

```java
    @Override
    public <T> Invoker<T> protocolBindingRefer(Class<T> serviceType, URL url) throws RpcException {
        ...
        // 先通过getClients方法创建客户端ExchangeClient对象
        //然后创建Dubbo调用器DubboInvoker对象
        DubboInvoker<T> invoker = new DubboInvoker<>(serviceType, url, getClients(url), invokers);
        invokers.add(invoker);

        return invoker;
    }
```

<font style="color:#080808;background-color:#ffffff;"> DubboProtocol的getClients方法中主要根据连接数处理了普通连接和共享连接的逻辑，默认情况下连接数配置为0为普通连接，共享连接配置默认为1，如下代码所示：</font>

```java
private ClientsProvider getClients(URL url) {
        int connections = url.getParameter(CONNECTIONS_KEY, 0);
        //如果未配置，则共享连接，默认的共享连接数是1
        if (connections == 0) {
            ...
            //用于指定共享连接的数量
            connections = Integer.parseInt(shareConnectionsStr);
            return getSharedClient(url, connections);
        }
        ...
        //如果配置来连接数则客户端根据连接数来创建连接
    }
```

这里要关注的是普通连接和共享连接，说明如下所示：

+ <font style="color:#080808;background-color:#ffffff;">普通连接connections：</font><font style="color:rgb(0, 0, 0);">用于指定每个服务的连接数。如果配置了 connections 参数，则每个服务将使用指定数量的独立连接。这意味着每个服务都有自己的连接，不会与其他服务共享连接。</font><font style="color:#080808;background-color:#ffffff;"> </font>
+ <font style="color:#080808;background-color:#ffffff;">共享连接shareConnections：</font><font style="color:rgb(0, 0, 0);">用于指定共享连接的数量。如果没有配置 connections 参数，则默认情况下，消费者和提供者 JVM 实例之间会共享一个长连接。通过配置 shareconnections 参数，可以设置共享长连接的数量，以避免共享单个长连接的瓶颈。</font>

<font style="color:#080808;background-color:#ffffff;">关于连接的创建我们主要以DubboProtocol的getSharedClient获取共享客户端连接为参考，来看下连接创建的过程，代码如下所示：</font>

```java
private SharedClientsProvider getSharedClient(URL url, int connectNum) {
        String key = url.getAddress();
        ...
        //key是IP:端口的形式如*************:20880，
        //！！！在共享连接中可以看到一个服务提供者只会对应一个客户端对象
        return referenceClientMap.compute(key, (originKey, originValue) -> {
            ...
            return new SharedClientsProvider(
                        this, originKey, buildReferenceCountExchangeClientList(url, expectedConnectNum));
            ...
        });
    }

```



<font style="color:#080808;background-color:#ffffff;"> 在DubboProtocol的buildReferenceCountExchangeClientList方法中根据连接数构建客户端如下代码所示：</font>

```java
private List<ReferenceCountExchangeClient> buildReferenceCountExchangeClientList(URL url, int connectNum) {
    List<ReferenceCountExchangeClient> clients = new ArrayList<>();
    for (int i = 0; i < connectNum; i++) {
        clients.add(buildReferenceCountExchangeClient(url));
    }
    return clients;
}
```

 

<font style="color:#080808;background-color:#ffffff;">继续来看构建单个ReferenceCountExchangeClient对象的buildReferenceCountExchangeClient方法，代码如下所示：</font>

```java
private ReferenceCountExchangeClient buildReferenceCountExchangeClient(URL url) {
        ExchangeClient exchangeClient = initClient(url);
        ReferenceCountExchangeClient client = new ReferenceCountExchangeClient(exchangeClient, DubboCodec.NAME);
        ...
        return client;
}
```

+ ExchangeClient：这是一个接口，定义了 Dubbo 中客户端通信的基本功能。它继承了 Client 和 ExchangeChannel 接口，提供了发送请求、接收响应、连接管理等功能。ExchangeClient 是 Dubbo 中网络通信的核心组件之一，负责与服务端进行数据交换。  
+ ReferenceCountExchangeClient：这是 ExchangeClient 的一个实现类，增加了引用计数功能。它通过引用计数来管理 ExchangeClient 的生命周期，确保在多个地方使用同一个 ExchangeClient 时，只有在所有引用都释放后才会真正关闭连接。这样可以提高资源利用率，避免频繁创建和销毁连接。

在Dubbo中ExchangeClient对象的构建会使用装饰器模式经过多个ExchangeClient实现的相互包装来增强功能。

接下来可以看下<font style="color:#080808;background-color:#ffffff;">DubboProtocol中的initClient方法是如何构建</font>ExchangeClient的，如下代码所示：

```java
private ExchangeClient initClient(URL url) {    
        ...
       //如果是延迟连接则创建LazyConnectExchangeClient来处理
       //默认非延迟连接在服务引用的时候创建连接
       return url.getParameter(LAZY_CONNECT_KEY, false)
                    ? new LazyConnectExchangeClient(url, requestHandler)
                    : Exchangers.connect(url, requestHandler);
        ...
    }
```



<font style="color:#080808;background-color:#ffffff;">在提供端导出服务的时候我们了解到交换器工具类Exchangers的bind端口方法，接下来可以看下connect方法连接提供端创建ExchangeClient对象的代码，如下所示：</font>

```java
public static ExchangeClient connect(URL url, ExchangeHandler handler) throws RemotingException {
        ...
        return getExchanger(url).connect(url, handler);
    }
```

<font style="color:#080808;background-color:#ffffff;">默认情况下获取到的交换器类型Exchanger类型为HeaderExchanger，在HeaderExchanger的connect方法中通过调用传输器Transporters的connect方法来建立连接</font>

```plain
@Override
public ExchangeClient connect(URL url, ExchangeHandler handler) throws RemotingException {
    return new HeaderExchangeClient(
            Transporters.connect(url, new DecodeHandler(new HeaderExchangeHandler(handler))), true);
}
```

<font style="color:#080808;background-color:#ffffff;">handler从内到外具有如下几个：</font>

+ <font style="color:#080808;background-color:#ffffff;"> DubboProtocol$1  DubboProtocol构造器中的匿名对象</font>
+ <font style="color:#080808;background-color:#ffffff;">HeaderExchangeHandler 负责处理客户端与服务端之间的消息交换，封装底层的 Client 和 ExchangeChannel，提供了请求发送、响应接收、连接管理等功能。该类还支持心跳检测和重连机制，以确保连接的稳定性和可靠性</font>
+ <font style="color:#080808;background-color:#ffffff;">DecodeHandler  调用解码器进行解码</font>

<font style="color:#080808;background-color:#ffffff;"> 在Dubbo中Exchange 层中主要负责消息的请求和响应处理，提供了更高级别的抽象，它封装了底层的 Transporter 层，提供了更高级别功能，如心跳检测、重连机制，</font><font style="color:rgb(33, 37, 41);">同步转异步，以 </font>`<font style="color:rgb(33, 37, 41);">Request</font>`<font style="color:rgb(33, 37, 41);">, </font>`<font style="color:rgb(33, 37, 41);">Response</font>`<font style="color:rgb(33, 37, 41);"> 为中心</font><font style="color:#080808;background-color:#ffffff;">等，Transporter 层则主要负责底层的网络传输，提供了基本的网络通信功能，如连接管理、数据传输等。</font>

<font style="color:#080808;background-color:#ffffff;">接下来看下传输器工具类Transporters来创建连接的connect方法</font>

```plain
public static Client connect(URL url, ChannelHandler... handlers) throws RemotingException {
    ...
    return getTransporter(url).connect(url, handler);
}
```

<font style="color:#080808;background-color:#ffffff;"> 传输层默认的传输器Transporter的扩展为NettyTransporter，在connect方法中创建了一个NettyClient类型对象，如下所示：</font>

```plain
@Override
public Client connect(URL url, ChannelHandler handler) throws RemotingException {
    return new NettyClient(url, handler);
}
```

<font style="color:#080808;background-color:#ffffff;"> NettyClient 是 Dubbo 中基于 Netty 框架实现的 Client 接口的一个实现类，负责处理客户端与服务端之间的网络通信。它利用 Netty 提供的异步和事件驱动机制，实现了高性能的网络传输功能，支持连接管理、数据传输和事件处理等操作。</font>

<font style="color:#080808;background-color:#ffffff;">NettyClient的构造器方法如下所示：</font>

```plain
public NettyClient(final URL url, final ChannelHandler handler) throws RemotingException {
    super(url, wrapChannelHandler(url, handler));
}
```

<font style="color:#080808;background-color:#ffffff;"> wrapChannelHandler 方法在 NettyClient 类中将提供的 ChannelHandler 包装成多个处理器，以添加额外的功能。然后调用NettyClient父类型AbstractClient的构造器，AbstractClient构造器中则执行客户端网络IO的处理方法，如下所示：</font>

```plain
public AbstractClient(URL url, ChannelHandler handler) throws RemotingException {
    super(url, handler);
    ...
    //初始化客户端网络IO线程池
    //客户端网络IO线程池为CachedThreadPool
    //线程名字：DubboClientHandler
    //核心线程数为0，最大为Integer.MAX_VALUE，队列数为0
    initExecutor(url);
 
    //初始化NettyClientHandler和Bootstrap
    doOpen();
     
    // connect.
    //执行 bootstrap.connect(serverAddress)连接服务端
    //在Netty中缓存网络通道Channel
    connect();
         
}
```

<font style="color:#080808;background-color:#ffffff;"> 在AbstractClient类型中仍旧会先执行其父类型构造器的处理，具体内容可以不进行展开，但是可以看下执行关系如下：</font>

+ <font style="color:#080808;background-color:#ffffff;"> Endpoint  接口</font>
+ <font style="color:#080808;background-color:#ffffff;">AbstractPeer  记录单个url和handler对象</font>
+ <font style="color:#080808;background-color:#ffffff;">AbstractEndpoint 初始化了编码解码器DubboCountCodec</font>
+ <font style="color:#080808;background-color:#ffffff;">AbstractClient  客户端网络IO模版类</font>

重点来看下客户端<font style="color:#080808;background-color:#ffffff;">AbstractClient的doOpen方法和connect方法，一个用来初始化网络客户端，一个用来建立连接，两者的最终实现都是位于其实现类NettyClient中，首先来看doOpen方法初始化网络信息如下所示：</font>

```java
    protected void doOpen() throws Throwable {
        //该对象负责处理 Netty 客户端的各种事件，如连接、断开、读取消息、写入消息和异常处理等
        //这里的NettyClientHandler对象中的ChannelHandler为NettyClient
        final NettyClientHandler nettyClientHandler = createNettyClientHandler();
        //Bootstrap 是 Netty 中用于配置和启动客户端的类，提供了简便的方法来设置客户端的各种参数和处理器
        bootstrap = new Bootstrap();
        //初始化bootstrap
        initBootstrap(nettyClientHandler);
    }
```

<font style="color:#080808;background-color:#ffffff;"> </font>

<font style="color:#080808;background-color:#ffffff;">NettyClient中通过initBootstrap方法来初始化bootstrap的网络参数和处理器如下所示：</font>

```java
protected void initBootstrap(NettyClientHandler nettyClientHandler) {
        bootstrap
                // 设置事件循环组，用于处理 I/O 操作
                .group(EVENT_LOOP_GROUP.get())
                // 设置通道选项：保持连接活跃
                .option(ChannelOption.SO_KEEPALIVE, true)
                // 设置通道选项：立即发送数据
                .option(ChannelOption.TCP_NODELAY, true)
                // 设置通道选项：使用池化的字节缓冲区分配器
                .option(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT)
                // 设置通道类型 EpollSocketChannel 或者 NioSocketChannel
                .channel(socketChannelClass());
        //设置连接超时时间 默认3秒
        bootstrap.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, Math.max(DEFAULT_CONNECT_TIMEOUT, getConnectTimeout()));
        // 构建 SSL 上下文
        SslContext sslContext = SslContexts.buildClientSslContext(getUrl());
        // 设置通道初始化器
        bootstrap.handler(new ChannelInitializer<SocketChannel>() {

            @Override
            protected void initChannel(SocketChannel ch) throws Exception {
                // 获取心跳间隔时间
                int heartbeatInterval = UrlUtils.getHeartbeat(getUrl());
                // 如果 SSL 上下文不为空，添加 SSL/TLS 处理器
                if (sslContext != null) {
                    ch.pipeline().addLast("negotiation", new SslClientTlsHandler(sslContext));
                }
                // 创建 Netty 编解码适配器
                NettyCodecAdapter adapter = new NettyCodecAdapter(getCodec(), getUrl(), NettyClient.this);
                ch.pipeline() 
                        // 添加解码器
                        .addLast("decoder", adapter.getDecoder())
                        // 添加编码器
                        .addLast("encoder", adapter.getEncoder())
                        // 添加心跳处理器
                        .addLast("client-idle-handler", new IdleStateHandler(heartbeatInterval, 0, 0, MILLISECONDS))
                        // 添加客户端处理器
                        .addLast("handler", nettyClientHandler);
                // 获取 SOCKS 代理主机地址
                String socksProxyHost =
                        ConfigurationUtils.getProperty(getUrl().getOrDefaultApplicationModel(), SOCKS_PROXY_HOST);
                // 如果 SOCKS 代理主机地址不为空且目标地址未被过滤，添加 SOCKS 代理处理器
                if (socksProxyHost != null && !isFilteredAddress(getUrl().getHost())) {
                    int socksProxyPort = Integer.parseInt(ConfigurationUtils.getProperty(
                            getUrl().getOrDefaultApplicationModel(), SOCKS_PROXY_PORT, DEFAULT_SOCKS_PROXY_PORT));
                    Socks5ProxyHandler socks5ProxyHandler =
                            new Socks5ProxyHandler(new InetSocketAddress(socksProxyHost, socksProxyPort));
                    ch.pipeline().addFirst(socks5ProxyHandler);
                }
            }
        });
    }
```

在initBootstrap方法中更多的是关于Dubbo如何使用Netty的Bootstrap来初始化网络参数和处理器等逻辑。

Bootstrap网络参数初始化完毕之后可以来看下connect方法调用的doConnect方法，如下代码所示：

```java
@Override
protected void doConnect() throws Throwable {
    ...
    doConnect(connectAddress);
    ...
}

private void doConnect(InetSocketAddress serverAddress) throws RemotingException {
    //这一行就是客户端向服务端 发起连接请求
    ChannelFuture future = bootstrap.connect(serverAddress);
    ...
    // 等待连接结果 默认连接超时为3秒
    boolean ret = future.awaitUninterruptibly(getConnectTimeout(), MILLISECONDS);
    if (ret && future.isSuccess()) {
        // 连接成功，获取新的通道
        Channel newChannel = future.channel();
        ...
        //将新通道缓存在NettyClient的成员变量中
        NettyClient.this.channel = newChannel;    
     }
     ...
}
```

 

<font style="color:#080808;background-color:#ffffff;"> 到这里接口级的服务引用与连接服务端的逻辑就结束了。下面可以再回过头看下应用级服务订阅逻辑。</font>

<font style="color:#080808;background-color:#ffffff;"> </font>

### <font style="color:#080808;background-color:#ffffff;"> 应用级服务订阅Invoker对象创建</font>
<font style="color:#080808;background-color:#ffffff;">回到应用级MigrationInvoker类型的migrateToApplicationFirstInvoker方法，前面我们详细对接口级订阅时创建的Invoker逻辑进行了说明，接下来可以看下应用服务发现模型中的Invoker对象的创建。</font>

```java
    @Override
    public void migrateToApplicationFirstInvoker(MigrationRule newRule) {
        CountDownLatch latch = new CountDownLatch(0);
        //刷新接口级调用器
        refreshInterfaceInvoker(latch);
        //刷新应用级调用器
        refreshServiceDiscoveryInvoker(latch);
        // 根据迁移规则计算出当前首选的调用器（Invoker）
        calcPreferredInvoker(newRule);
    }
```

<font style="color:#080808;background-color:#ffffff;"></font>

<font style="color:#080808;background-color:#ffffff;">应用级的服务发现与接口级思路略微不同整体过程高度详细，在看具体细节之前可以来整体看下，如下图所示：</font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1736952443547-c7099224-a20f-47c6-9e91-a7a75a7e7c6c.png)

<font style="color:#080808;background-color:#ffffff;">接下来通过入口refreshServiceDiscoveryInvoker方法来看消费者应用级服务发现，订阅的 ，代码如下：</font>

```java
protected void refreshServiceDiscoveryInvoker(CountDownLatch latch) {
        ...
        serviceDiscoveryInvoker = registryProtocol.getServiceDiscoveryInvoker(cluster, registry, type, url);
        ...
    }
```

<font style="color:#080808;background-color:#ffffff;"> </font>

<font style="color:#080808;background-color:#ffffff;">InterfaceCompatibleRegistryProtocol的getServiceDiscoveryInvoker方法</font>

```java
    @Override
    public <T> ClusterInvoker<T> getServiceDiscoveryInvoker(
            Cluster cluster, Registry registry, Class<T> type, URL url) {
        //服务发现注册中心ServiceDiscoveryRegistry
        registry = getRegistry(super.getRegistryUrl(url));
        //服务发现模型的注册中心目录
        DynamicDirectory<T> directory = new ServiceDiscoveryRegistryDirectory<>(type, url);
        //同接口级Invoker对象创建过程一样
        return doCreateInvoker(directory, cluster, registry, type);
    }
```

这个方法中通过super.getRegistryUrl(url) 将zookeeper协议类型的注册中心url转换为服务发现注册url如下：

service-discovery-registry://127.0.0.1:2181/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default&application=dubbo-demo-consumer&dubbo=2.0.2&environment=product&executor-management-mode=isolation&file-cache=true&logger=slf4j&pid=48713&qos.port=22223&registry=zookeeper&release=3.3.0&timestamp=1731369774007

然后通过<font style="color:#080808;background-color:#ffffff;">RegistryFactory扩展ServiceDiscoveryRegistryFactory来创建</font><font style="color:#080808;background-color:#ffffff;">ServiceDiscoveryRegistry</font>

<font style="color:#080808;background-color:#ffffff;"> </font>

<font style="color:#080808;background-color:#ffffff;">应用级Invoker创建的过程中同样是执行RegistryProtocol的doCreateInvoker方法，不同的是使用到的服务发现类型为应用级比如registry类型为ServiceDiscoveryRegistry，directory类型为ServiceDiscoveryRegistryDirectory，接下来我们就来看下应用级Invoker对象创建过程是如何处理服务注册与服务订阅的，doCreateInvoker代码如下所示：</font>

```java
 protected <T> ClusterInvoker<T> doCreateInvoker(
            DynamicDirectory<T> directory, Cluster cluster, Registry registry, Class<T> type) {
        
        ...
        //消费者服务注册 应用级无需注册接口
        registry.register(directory.getRegisteredConsumerUrl());
        //消费者路由执行链构建
        directory.buildRouterChain(urlToRegistry);
        //消费者订阅服务
        directory.subscribe(toSubscribeUrl(urlToRegistry));
        //创建集群调用器
        return (ClusterInvoker<T>) cluster.join(directory, true);
    }
```

<font style="color:#080808;background-color:#ffffff;"> 应用级的Invoker创建过程是不进行消费者接口级注册，这个可以忽略，主要的逻辑是在消费者订阅服务时的逻辑，接下来就重点来看消费者通过ServiceDiscoveryRegistryDirectory服务目录来订阅服务</font>

<font style="color:#080808;background-color:#ffffff;">在subscribe方法中服务发现目录的subscribe方法会调用服务发现注册的subscribe方法，主要执行链路如下所示，具体代码比较简单不做展开：</font>

+ <font style="color:#080808;background-color:#ffffff;">ServiceDiscoveryRegistryDirectory subscribe</font>
+ <font style="color:#080808;background-color:#ffffff;">DynamicDirectory subscribe</font>
+ <font style="color:#080808;background-color:#ffffff;"> ListenerRegistryWrapper subscribe</font>
+ <font style="color:#080808;background-color:#ffffff;">ServiceDiscoveryRegistry doSubscribe</font>

<font style="color:#080808;background-color:#ffffff;"> </font>

<font style="color:#080808;background-color:#ffffff;">直接来看ServiceDiscoveryRegistry的doSubscribe方法，其中listener参数为ServiceDiscoveryRegistryDirectory类型对象，在doSubscribe方法中主要逻辑为处理接口到应用的映射关系，然后就是向提供端发起订阅</font>

```java
@Override
public void doSubscribe(URL url, NotifyListener listener) {
    ...
     //监听服务映射元数据变更，如果存在变更则刷新缓存，订阅变更的系统
     MappingListener mappingListener = new DefaultMappingListener(url, mappingByUrl, listener);
    //从AbstractServiceNameMapping的mappingCacheManager缓存中获取当前服务对应的系统信息，
    //如果不存在则从元数据中心获取
    //当前案例中获取到的数据为dubbo-demo-provider
    mappingByUrl = serviceNameMapping.getAndListen(this.getUrl(), url, mappingListener);
     ...
    subscribeURLs(url, listener, mappingByUrl);
}
```

<font style="color:#080808;background-color:#ffffff;"> 在提供者导出服务完成之后会在exported方法中执行registerServiceAppMapping将接口到应用信息的映射关注注册在元数据中心中，消费者在这里进行服务订阅的时候先找到当前服务接口由哪些提供者提供</font>

<font style="color:#080808;background-color:#ffffff;">然后先将数据缓存在本地磁盘缓存、内存缓存中方便后续查询，这里查询到服务名之后则开始进行应用级服务订阅。</font>

<font style="color:#080808;background-color:#ffffff;">服务映射节点信息如下：</font>

```bash
127.0.0.1:2181	$	get /dubbo/mapping/org.demo.app.GreetingsService
dubbo-demo-provider
```

<font style="color:#080808;background-color:#ffffff;"></font>

<font style="color:#080808;background-color:#ffffff;">ServiceDiscoveryRegistry的subscribeURLs方法中我们一共要关注几个地方：</font>

+ <font style="color:#080808;background-color:#ffffff;">serviceDiscovery.getInstances(serviceName) 根据服务名字查询应用级实例数据</font>
+ <font style="color:#080808;background-color:#ffffff;">serviceInstancesChangedListener.onEvent 根据应用实例数据更新接口级数据。</font>

<font style="color:#080808;background-color:#ffffff;"></font>

```java
protected void subscribeURLs(URL url, NotifyListener listener, Set<String> serviceNames) {
    ...
    serviceInstancesChangedListener = serviceDiscovery.createListener(serviceNames);

    for (String serviceName : serviceNames) {
        //根据服务名从服务发现注册中心中查询服务实例信息列表
        List<ServiceInstance> serviceInstances = serviceDiscovery.getInstances(serviceName);
        if (CollectionUtils.isNotEmpty(serviceInstances)) {
            //服务实例不为空则触发 服务实例改变事件
            serviceInstancesChangedListener.onEvent(
                new ServiceInstancesChangedEvent(serviceName, serviceInstances));
        }
    }
    //触发服务目录的refreshOverrideAndInvoker
    serviceInstancesChangedListener.addListenerAndNotify(url, listener);
    ...
    //创建针对注册中心中的serviceName服务实例监听，当注册中心服务实例中的数据发生变更的时候触发
    //ServiceInstancesChangedListener的onEvent方法进行刷新实例
    serviceDiscovery.addServiceInstancesChangedListener(finalServiceInstancesChangedListener);

    ... 

}
```

<font style="color:#080808;background-color:#ffffff;">同一个服务接口可能有多个提供端应用进行提供，在订阅方法中先通过遍历服务名字serviceNames，然后使用ZookeeperServiceDiscovery的getInstances进行查询应用级服务实例数据，Dubbo中基于Zookeeper的应用级服务发现依赖于Curator（Zookeeper客户端）来调用注册中心根据服务名字查询服务实例数据，应用级服务发现信息如下：</font>

<font style="color:#080808;background-color:#ffffff;">对应路径为：</font>


<font style="color:#080808;">services/dubbo-demo-provider/***************:20880</font>

<br/>

<font style="color:#080808;background-color:#ffffff;">对应服务实例数据如下所示：</font>

```java
DefaultServiceInstance{
    serviceName='dubbo-demo-provider', 
    host='***************', 
    port=20880, 
    enabled=true, 
    healthy=true, 
    metadata={
        dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}],
        dubbo.metadata-service.url-params={
            "prefer.serialization":"hessian2,fastjson2",
            "version":"1.0.0",
            "dubbo":"2.0.2",
            "release":"3.3.2",
            "side":"provider",
            "ipv6":"240e:46c:a000:9f7:82a:b8a8:624e:1dae",
             "port":"20880",
             "protocol":"dubbo"
     },
    dubbo.metadata.revision=1642d72ec7f79a4bedb6bebc9101b90e,   	
    dubbo.metadata.storage-type=local, 
    ipv6=240e:46c:a000:9f7:82a:b8a8:624e:1dae, 
    meta-v=1.0.0,
    timestamp=1736899551590
    }
}
```

<font style="color:#080808;background-color:#ffffff;">应用级的服务发现主要查询实例数据包含服务的主机信息，版本号等等，在实际服务调用中还需要接口级数据，Dubbo3的应用级服务发现模型中默认将将接口级的元数据下沉到提供端存储，这样每个接口将共用的服务实例数据，服务实例数据仅仅需要注册一份即可，消费端则通过RPC调用提供端来获取元数据，有效的减轻了注册中心的存储压力与访问压力。</font>

<font style="color:#080808;background-color:#ffffff;">接口级的元数据获取逻辑则位于ServiceInstancesChangedListener的onEvent方法中，当实例发生变化时比如：注册、注销、更新都会执行onEvent来刷新实例，这里是在进程启动主动订阅的情况下触发了onEvent方法，后续则会通过订阅通知事件来触发onEvent方法。</font>

<font style="color:#080808;background-color:#ffffff;">ServiceInstancesChangedListener的onEvent 刷新服务实例</font>

```java
public void onEvent(ServiceInstancesChangedEvent event) {
    ...
    doOnEvent(event);
}
```

<font style="color:#080808;background-color:#ffffff;">ServiceInstancesChangedListener的doOnEvent 刷新服务实例</font>

```java
private synchronized void doOnEvent(ServiceInstancesChangedEvent event) {

    ...
    //根据版本号将当前实例添加到版本分组的集合revisionToInstances中
    Map<String, List<ServiceInstance>> revisionToInstances = new HashMap<>();

    // get MetadataInfo with revision
    for (Map.Entry<String, List<ServiceInstance>> entry : revisionToInstances.entrySet()) {
        String revision = entry.getKey();
        List<ServiceInstance> subInstances = entry.getValue();

        //根据提供者实例中的元数据版本号获取提供者接口级元数据
        MetadataInfo metadata = subInstances.stream()
        //获取服务元数
        .map(ServiceInstance::getServiceMetadata)
        //过滤非空元数据
        .filter(Objects::nonNull)
        //过滤匹配的修订版本
        .filter(m -> revision.equals(m.getRevision()))
        //查找第一个匹配的元数据
        .findFirst()
        //前面逻辑未匹配到时候则根据版本获取元数据
        .orElseGet(() -> serviceDiscovery.getRemoteMetadata(revision, subInstances));
        ...
    }
    ...
}
```

<font style="color:#080808;background-color:#ffffff;">前面进行了应用级实例数据和接口级元数据的查询，接下来可以看下</font>

服务通知方法，<font style="color:#080808;background-color:#ffffff;">serviceInstancesChangedListener的addListenerAndNotify(url, listener)，这个方法中listener参数为ServiceDiscoveryRegistryDirectory类型对象，核心代码如下所示：</font>

```java
public synchronized void addListenerAndNotify(URL url, NotifyListener listener) {
        ...
        //回调通知，urls集合类型为InstanceAddressURL
        listener.notify(urls);
        ...
    }
```

<font style="color:#080808;background-color:#ffffff;">在接口级服务订阅通知的时候我们了解到接口级服务通知方法是经过RegistryDirectory类型的notify方法，这里应用级服务发现进行服务通知的时候通过调用ServiceDiscoveryRegistryDirectory的notify方法来刷新数据，应用级服务通知与接口级类似，同样是调用了refreshOverrideAndInvoker来覆盖配置和刷新Invoker对象，但是这里的类型为ServiceDiscoveryRegistryDirectory，代码如下所示：</font>

```java
 @Override
    public synchronized void notify(List<URL> instanceUrls) {
        ...
        refreshOverrideAndInvoker(instanceUrls);
    }
```

<font style="color:#080808;background-color:#ffffff;"> </font>

<font style="color:#080808;background-color:#ffffff;">继续看ServiceDiscoveryRegistryDirectory的refreshOverrideAndInvoker</font>

```java
  @Override
    protected synchronized void refreshOverrideAndInvoker(List<URL> instanceUrls) {
        //应用级通知方法中覆盖配置使用app-name.configurators和service-name.configurators
        this.directoryUrl = overrideDirectoryWithConfigurator(getOriginalConsumerUrl());
        refreshInvoker(instanceUrls);
    }
```

应用级通知方法中<font style="color:#080808;background-color:#ffffff;"> overrideDirectoryWithConfigurator与接口级的略微不同，同样是用于更新订阅到的提供者配置刷新，这里已经不再使用override协议的配置覆盖，而是仅仅使用如下两种配置：</font>

+ <font style="color:#080808;background-color:#ffffff;">应用级配置：app-name.configurators</font>
+ <font style="color:#080808;background-color:#ffffff;">接口级配置：service-name.configurators</font>

<font style="color:#080808;background-color:#ffffff;"></font>

<font style="color:#080808;background-color:#ffffff;">应用级服务通知时的ServiceDiscoveryRegistryDirectory类型的refreshInvoker方法与接口级处理又有哪些不同的，可以直接看如下代码：</font>

```java
private void refreshInvoker(List<URL> invokerUrls) {
        if (invokerUrls.size() == 1 && EMPTY_PROTOCOL.equals(invokerUrls.get(0).getProtocol())) {
            ...
            //当没有任何提供者可用的时候将会 设置状态为禁止访问
            this.forbidden = true // Forbid to access
            destroyAllInvokers(); // Close all invokers
        } else {
            this.forbidden = false; // Allow accessing
            ...
            //将 URL 列表转换为 Invoker 映射
            Map<ProtocolServiceKeyWithAddress, Invoker<T>> newUrlInvokerMap =
                    toInvokers(oldUrlInvokerMap, invokerUrls); 
            ...
            //存在不使用的Invoker则销毁
            if (oldUrlInvokerMap != null) {
                ...
                destroyUnusedInvokers(oldUrlInvokerMap, newUrlInvokerMap); // Close the unused Invoker
                ...
            }
        }
        // notify invokers refreshed
        this.invokersChanged();
        ...
    }
```

<font style="color:#080808;background-color:#ffffff;"> 可以看到应用级服务发现中ServiceDiscoveryRegistryDirectory类型的refreshInvoker和接口级服务发现中的RegistryDirectory的refreshInvoker方法高度相似。</font>

<font style="color:#080808;background-color:#ffffff;">然后继续看URL列表转换为Invoker对象的toInvokers方法，如下所示：</font>

```java
private Map<ProtocolServiceKeyWithAddress, Invoker<T>> toInvokers(
    Map<ProtocolServiceKeyWithAddress, Invoker<T>> oldUrlInvokerMap, List<URL> urls) {
    ...
    for (URL url : urls) {
        InstanceAddressURL instanceAddressURL = (InstanceAddressURL) url;
        ...
        //如果协议类型不支持则跳过
        if (!getUrl().getOrDefaultFrameworkModel()
            .getExtensionLoader(Protocol.class)
            .hasExtension(instanceAddressURL.getProtocol())) {
            ...
            continue;
        }
            ...
        //matchedProtocolServiceKeys为url转化过来的，主要包含协议+服务信息作为一个服务的唯一标识
        for (ProtocolServiceKey matchedProtocolServiceKey : matchedProtocolServiceKeys) {
            //从缓存中查询invoker对象
            Invoker<T> invoker =
            oldUrlInvokerMap == null ? null : oldUrlInvokerMap.get(protocolServiceKeyWithAddress);
            //如果缓存中不存在或者url发生了变更，则将会判断是否需要重新引用
            if (invoker == null
                || urlChanged(
                    invoker,
                    instanceAddressURL,
                    matchedProtocolServiceKey)) { 
                ...
                //检查服务是否启用
                boolean enabled;
                if (instanceAddressURL.hasParameter(DISABLED_KEY)) {
                    enabled = !instanceAddressURL.getParameter(DISABLED_KEY, false);
                } else {
                    enabled = instanceAddressURL.getParameter(ENABLED_KEY, true);
                }
                //如果服务处于启用状态则开始应用服务
                if (enabled) {
                    ...
                    invoker = protocol.refer(serviceType, instanceAddressURL);

                }
                ...
                if (invoker != null) { 
                    newUrlInvokerMap.put(protocolServiceKeyWithAddress, invoker);
                }
                ...
            }
            return newUrlInvokerMap;
        }
```

可以看到应用级的URL转换为Invoker方法也是高度相似，先检查url是否支持，然后检查url是否发生了变更，如果发生了变更则执行引用，服务引用的时候这里使用的协议类型为<font style="color:#080808;background-color:#ffffff;">dubbo协议，这里执行的服务引用逻辑同接口级是一样的，也是要经过如下过程：</font>

+ <font style="color:#080808;background-color:#ffffff;">Protocol$Adaptive 用于动态适配不同的协议实现，根据 URL 中的协议参数选择具体的协议实现。</font>
+ <font style="color:#080808;background-color:#ffffff;">...协议Wrapper类型</font>
+ <font style="color:#080808;background-color:#ffffff;">DubboProtocol Dubbo 默认的协议实现，负责处理 Dubbo 协议的具体逻辑</font>

<font style="color:#080808;background-color:#ffffff;">需要注意的是在接口级服务引用的时候在DubboProtocol的getSharedClient方法中使用IP:PORT为KEY来创建、缓存网络客户端对象，这里应用级服务发现调用DubboProtocol的getSharedClient方法的时候将会从缓存中直接查询到已经创建好的网络客户端对象而无需再次针对同一个IP:PORT来创建网络客户端，具体代码如下所示：</font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1736951333485-8dbf1139-78a9-4581-ac22-43582ee7bbf9.png)

到目前为止消费者的接口级引用和应用级服务引用已经完成了，可以看到无论提供者启动或者消费者启动的整个过程都是围绕最开始我们了解到的服务发现模型来看的，在服务发现模型的最后消费者将执行RPC调用提供端，具体逻辑可以看后续章节。

