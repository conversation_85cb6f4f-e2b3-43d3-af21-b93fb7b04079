### 三层领域模型的概念
前面案例中提供者和消费者都使用了DubboBootstrap来启动服务，在DubboBootstrap源码中会初始化三个领域模型FrameworkModel、ApplicationModel 和 ModuleModel，对这三个模型的理解非常重要，它们将会贯穿整个Dubbo程序启动的生命周期。

如何去理解这三个模型呢，先来通过一个展示层级的图来看下它们的关系，如下所示：

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1735913264946-e9ff9519-09fe-4690-947a-11cb24fcff4f.png)

这三个类型分别对应了三个层级，JVM、 应用、 模块。通过这些领域模型来实现不同级别的生命周期与配置的管理，对应三个类型的理解见下表。

| 类型 | 说明 | 场景 |
| --- | --- | --- |
| FrameworkModel | 框架级模型 | 全局运行环境，适配多应用混合部署的场景，降低资源成本。 |
| ApplicationModel | 应用级模型 | <font style="color:rgb(34, 34, 34);">系统下的子应用运行环境，适配单JVM多应用场景</font>。 |
| ModuleModel | <font style="color:rgb(34, 34, 34);">模块级模型</font> | <font style="color:rgb(34, 34, 34);">应用下的子模块运行环境，适配一个应用下的多个模块（容器）</font> |


到这里就了解到了三个领域模型的概念，但是还是相对抽象，使用三个领域模型实现数据，功能的隔离，如下所示：

+ 企业要使用一个单体应用来实现多租户商城系统，这个系统有多个租户每个租户需要将各自的三大中心（注册、配置、元数据），监控，线程池等资源实现隔离。
+ 每个租户都有各自的商城、订单、交易、结算等子系统，子系统和子系统之间又有各自的配置。
+ 每个子系统又有多个模块，比如商城系统有内部管理员使用的商品统计模块，和外部商品的管理模块

整体隔离模型如下：

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1735914486803-75c3ee40-bcc2-4d40-a0ca-551f88cf2f42.png)

下面就以一个例子来看下如何使用三个领域模型来实现隔离。

首先创建订单服务接口和订单服务实现用来处理订单逻辑 

```java
public interface OrderService {
}
```

 

```java
public class OrderServiceImpl implements OrderService {
}
```

 然后通过创建ModelDemo来创建多个租户的程序，主要包含如下逻辑：

+ 创建两个FrameworkModel代表两个租户，分别调用start方法启动它们。
+ 每个FrameworkModel中有各自的ApplicationModel，ApplicationModel中进行应用级的配置，这里我们使用不同的服务端口。
+ 每个ApplicationModel又有各自的ModuleModel，ModuleModel中进行各自的服务提供者配置。

```java

public class ModelDemo {
    private static final Logger logger = LoggerFactory.getLogger(ModelDemo.class);

    public static void main(String[] args) throws InterruptedException {
        //创建第一个租户的框架级FrameworkModel 并启动程序
        start(new FrameworkModel(), 20880);
        logger.info("frameworkModelOne start success");

        //创建第二个租户的框架级FrameworkModel，并启动程序
        start(new FrameworkModel(), 20881);
        new CountDownLatch(1).await();
    }

    private static void start(FrameworkModel frameworkModel, int port) {
        //创建订单子系统应用级ApplicationModel
        ApplicationModel orderApplicationModel = frameworkModel.newApplication();
        //创建模块级ModuleModel
        ModuleModel moduleModel = orderApplicationModel.newModule();
        //Model的配置管理通过对应的ConfigManager进行
        ConfigManager appConfigManager = orderApplicationModel.getApplicationConfigManager();
        //应用配置
        ApplicationConfig application = new ApplicationConfig("dubbo-provider-app");
        //关闭QOS避免端口冲突
        application.setQosEnable(false);
        appConfigManager.setApplication(application);

        //应用层级的配置中心、元数据中心、协议默认设置
        appConfigManager.addRegistry(new RegistryConfig("zookeeper://127.0.0.1:2181"));
        appConfigManager.addMetadataReport(new MetadataReportConfig("zookeeper://127.0.0.1:2181"));
        appConfigManager.addProtocol(new ProtocolConfig(CommonConstants.DUBBO, port));

        //配置模块
        ModuleConfigManager moduleConfigManager = moduleModel.getConfigManager();
        moduleConfigManager.setModule(new ModuleConfig("dubbo-provider-app-module"));

        ServiceConfig<OrderService> serviceConfig = new ServiceConfig<>();
        //设置该ServiceConfig对应的ModuleModel
        serviceConfig.setScopeModel(moduleModel);
        serviceConfig.setInterface(OrderService.class);
        serviceConfig.setRef(new OrderServiceImpl());
        //为ModuleModel添加ServiceConfig
        moduleConfigManager.addConfig(serviceConfig);
        //导出服务
        serviceConfig.export();
    }
}
```

启动程序观察注册中心就可以看到两个不同端口的Dubbo实例

![](https://cdn.nlark.com/yuque/0/2024/png/35284387/1724543851138-e1619ce6-dc20-4ede-b3b0-2e2b7b8014a5.png)

### 三层领域模型的初始化源码
**单例模式获取****<font style="color:#080808;background-color:#ffffff;">DubboBootstrap实例</font>**

前面了解了三层领域模型的概念，再回到提供者消费者案例中的启动代码，提供者和消费者启动时候均通过`<font style="color:#080808;background-color:#ffffff;">DubboBootstrap.getInstance() </font>`<font style="color:#080808;background-color:#ffffff;">来获取单例的启动器对象来启动程序，接下来我们就来看下三层领域模型的初始化，首先从启动器初始化开始。</font>

<font style="color:#080808;background-color:#ffffff;">在DubboBootstrap类型中通过单例模式获取DubboBootstrap实例对象</font>

```java
/**
 * 使用默认的应用级模型进行创建
 */
public static DubboBootstrap getInstance() {
    if (instance == null) {
        synchronized (DubboBootstrap.class) {
            if (instance == null) {
                instance = DubboBootstrap.getInstance(ApplicationModel.defaultModel());
            }
        }
    }
    return instance;
}
/**
 * 根据参数传递的应用级模型对象创建对应的启动器，一个应用级模型对象对应一个启动器
 */
public static DubboBootstrap getInstance(ApplicationModel applicationModel) {
return ConcurrentHashMapUtils.computeIfAbsent(
    instanceMap, applicationModel, _k -> new DubboBootstrap(applicationModel));
}
```

<font style="color:#080808;background-color:#ffffff;">可以看到在Dubbo中一个DubboBootstrap类型的启动器对象，对应一个应用级模型，在未传递应用级模型对象参数的时候getInstance无参重载方法会为我们创建一个默认的应用级模型对象，接下来就先来看下默认的模型对象是如何创建的。</font>

```java
/**
 * 在销毁默认 FrameworkModel 期间，FrameworkModel.defaultModel() 
 * 或 ApplicationModel.defaultModel()
 * 将返回一个损坏的模型，可能会导致不可预测的问题。
 * 建议：尽量避免使用默认模型。
 *
 * @return 全局默认的ApplicationModel
 */
public static ApplicationModel defaultModel() {
    // should get from default FrameworkModel, avoid out of sync
    return FrameworkModel.defaultModel().defaultApplication();
}
```

<font style="color:#080808;background-color:#ffffff;">FrameworkModel.defaultModel()方法中通过双重校验锁的单例模式创建默认实例如下所示：</font>

```java
public static FrameworkModel defaultModel() {
    FrameworkModel instance = defaultInstance;
    if (instance == null) {
        synchronized (globalLock) {
            resetDefaultFrameworkModel();
            if (defaultInstance == null) {
                defaultInstance = new FrameworkModel();
            }
            instance = defaultInstance;
        }
    }
    Assert.notNull(instance, "Default FrameworkModel is null");
    return instance;
}
```

**<font style="color:#080808;background-color:#ffffff;">FrameworkModel的初始化</font>**

<font style="color:#080808;background-color:#ffffff;">可以看到在默认的应用级模型创建之前要先获取默认的FrameworkModel对象，那我们就从FrameworkModel的初始化来看起。</font>

```java
/**
 * dubbo框架模型，可以被多个应用程序共享。
 */
public class FrameworkModel extends ScopeModel {

    public static final String NAME = "FrameworkModel";

    ...
    //所有的框架级模型实例
    private static final List<FrameworkModel> allInstances = new CopyOnWriteArrayList<>();

    ...
    //当前框架级模型下的所有应用级模型
    private final List<ApplicationModel> applicationModels = new CopyOnWriteArrayList<>();
    
    ...
    //框架级服务存储，方便操作当前框架下的提供者和消费者模型
    private final FrameworkServiceRepository serviceRepository;
    
    ...
    public FrameworkModel() {
        ...
        //初始化方法，调用父类ScopeModel中的initialize进行初始化
        initialize();
        //框架级服务存储管理
        serviceRepository = new FrameworkServiceRepository(this);
        ...
        //域模型初始化扩展，在初始化FrameworkModel时候可以进行初始化或者向框架级FrameworkModel容器中注册对象
        ExtensionLoader<ScopeModelInitializer> initializerExtensionLoader = this.getExtensionLoader(ScopeModelInitializer.class);
        Set<ScopeModelInitializer> initializers = initializerExtensionLoader.getSupportedExtensionInstances();
        //遍历域模型初始化列表，调用其initializeFrameworkModel方法
        for (ScopeModelInitializer initializer : initializers) {
              initializer.initializeFrameworkModel(this);
        }
        ...  
           
    }
    ...
}
```

<font style="color:#080808;background-color:#ffffff;">上面代码中主要列举了FrameworkModel初始化的构造器，在这个方法中有两个地方需要我们重点关注的，第一个地方是</font>`<font style="color:#080808;background-color:#ffffff;">initialize()</font>`<font style="color:#080808;background-color:#ffffff;">方法，这个方法来源于父类型ScopeModel，主要用于初始化Bean容器管理的实例，具体实现就在后面的ScopeModel初始化的章节来说明。</font>

<font style="color:#080808;background-color:#ffffff;">另外一个比较重要的生命周期调用就是获取ScopeModelInitializer实例来调用其生命周期方法</font>`<font style="color:#080808;background-color:#ffffff;">initializeFrameworkModel</font>`<font style="color:#080808;background-color:#ffffff;">，在这个方法中我们可以在框架级的初始化的时候做一些自定义初始化。</font>

<font style="color:#080808;background-color:#ffffff;">具体有哪些初始化的逻辑会在框架级模型初始化的时候进行初始化呢，下面可以通过一个表格来了解下这些初始化类型，方便后面用到时候知道所在类型是何时进行初始化的也方便在用到的时候知道应该从哪个容器中获取：</font>

| **<font style="color:rgb(51, 51, 51);">域模型初始化扩展</font>** | **初始化涉及到的类型** | **说明** |
| :--- | :--- | :--- |
| <font style="color:rgb(51, 51, 51);">CertScopeModelInitializer</font> | <font style="color:rgb(51, 51, 51);">DubboCertManager</font> | <font style="color:rgb(51, 51, 51);">Dubbo证书管理</font> |
| <font style="color:rgb(51, 51, 51);">ClusterScopeModelInitializer</font> | <font style="color:rgb(51, 51, 51);">RouterSnapshotSwitcher</font> | <font style="color:rgb(51, 51, 51);">路由快照打印开关</font> |
| <font style="color:rgb(51, 51, 51);"></font><br/><font style="color:rgb(51, 51, 51);"></font><br/><font style="color:rgb(51, 51, 51);"></font><br/><font style="color:rgb(51, 51, 51);">CommonScopeModelInitializer</font> | <font style="color:rgb(51, 51, 51);">FrameworkExecutorRepository</font> | <font style="color:rgb(51, 51, 51);">框架级线程池管理</font> |
| | <font style="color:rgb(51, 51, 51);">ConverterUtil</font> | <font style="color:rgb(51, 51, 51);">类型转换工具类</font> |
| | <font style="color:rgb(51, 51, 51);">SerializeSecurityManager</font> | <font style="color:rgb(51, 51, 51);">序列化安全管理</font> |
| | <font style="color:rgb(51, 51, 51);">DefaultSerializeClassChecker</font> | <font style="color:rgb(51, 51, 51);">默认的序列化类检查器</font> |
| | <font style="color:rgb(51, 51, 51);">CertManager</font> | <font style="color:rgb(51, 51, 51);">证书管理</font> |
| | <font style="color:rgb(51, 51, 51);">ClassHolder</font> | <font style="color:rgb(51, 51, 51);">类缓存器</font> |
| <font style="color:rgb(51, 51, 51);">ConfigScopeModelInitializer</font> | <font style="color:rgb(51, 51, 51);">FrameworkModelCleaner</font> | <font style="color:rgb(51, 51, 51);">框架级模型清理器</font> |
| <font style="color:rgb(51, 51, 51);">RegistryScopeModelInitializer</font> | <font style="color:rgb(51, 51, 51);">ExporterFactory</font> | <font style="color:rgb(51, 51, 51);">服务导出器工厂</font> |
| <font style="color:rgb(51, 51, 51);"></font><br/><font style="color:rgb(51, 51, 51);">TripleScopeModelInitializer</font> | <font style="color:rgb(51, 51, 51);">DefaultRequestMappingRegistry</font> | <font style="color:rgb(51, 51, 51);">默认的triple请求映射注册表</font> |
| | <font style="color:rgb(51, 51, 51);">DefaultRequestRouter</font> | <font style="color:rgb(51, 51, 51);">默认的triple请求路由类</font> |
| <font style="color:rgb(51, 51, 51);">Fastjson2ScopeModelInitializer</font> | <font style="color:rgb(51, 51, 51);">Fastjson2CreatorManager</font> | <font style="color:rgb(51, 51, 51);">Fastjson2的对象读写创建器</font> |
| | <font style="color:rgb(51, 51, 51);">Fastjson2SecurityManager</font> | <font style="color:rgb(51, 51, 51);">Fastjson2序列化安全管理类</font> |
| <font style="color:rgb(51, 51, 51);">Hessian2ScopeModelInitializer</font> | <font style="color:rgb(51, 51, 51);">Hessian2FactoryManager</font> | <font style="color:rgb(51, 51, 51);">Hession2序列化工厂管理类</font> |
| <font style="color:rgb(51, 51, 51);">QosScopeModelInitializer</font> | <font style="color:rgb(51, 51, 51);">Server</font> | <font style="color:rgb(51, 51, 51);">Qos服务端管理</font> |
| | <font style="color:rgb(51, 51, 51);">SerializeCheckUtils</font> | <font style="color:rgb(51, 51, 51);">Qos序列化检查工具类</font> |
| <font style="color:rgb(51, 51, 51);">RpcScopeModelInitializer</font> | <font style="color:rgb(51, 51, 51);">InjvmExporterListener</font> | <font style="color:rgb(51, 51, 51);">JVM内部导出器的监听器</font> |
| | <font style="color:rgb(51, 51, 51);">PermittedSerializationKeeper</font> | <font style="color:rgb(51, 51, 51);">请求序列化类型准入检查</font> |
| <font style="color:rgb(51, 51, 51);">SerializationScopeModelInitializer</font> | <font style="color:rgb(51, 51, 51);">PreferSerializationProviderImpl</font> | <font style="color:rgb(51, 51, 51);">优先序列化提供服务目前版本默认有 hessian2,fastjson2</font> |


<font style="color:#080808;background-color:#ffffff;">可以看到框架级模型初始化的时候存在很多框架级的类型需要进行初始化，对于这些类型的初始化暂时可以简单认识一下，方便看到时候有所了解，这里我们以</font><font style="color:rgb(51, 51, 51);">CommonScopeModelInitializer扩展的FrameworkExecutorRepository框架级线程池管理类型的初始化来举例说明，代码如下所示。</font>

```java
public class CommonScopeModelInitializer implements ScopeModelInitializer {
    @Override
    public void initializeFrameworkModel(FrameworkModel frameworkModel) {
        //获取Bean工厂
        ScopeBeanFactory beanFactory = frameworkModel.getBeanFactory();
        //注册Bean到Bean工厂
        beanFactory.registerBean(FrameworkExecutorRepository.class);
        ...
    }
```

<font style="color:rgb(51, 51, 51);"> 可以看到框架级模型的初始化方法中将</font>`<font style="color:rgb(51, 51, 51);">FrameworkExecutorRepository.class</font>`<font style="color:rgb(51, 51, 51);">注册到Bean容器中，这个方法内部会通过反射创建对象，然后将其添加至Bean工厂的集合中，方便后续使用。</font>

**<font style="color:#080808;background-color:#ffffff;">ApplicationModel的初始化</font>**

<font style="color:#080808;background-color:#ffffff;">再回过头来看ApplicationModel的初始化逻辑，在FrameworkModel中通过defaultApplication方法来创建默认的ApplicationModel，这个方法使用同样使用双重校验锁的单例模式来创建如下代码所示：</font>

```java
public ApplicationModel defaultApplication() {
    ApplicationModel appModel = this.defaultAppModel;
    if (appModel == null) {
        ...
        if ((appModel = this.defaultAppModel) == null) {
            synchronized (instLock) {
                if (this.defaultAppModel == null) {
                    this.defaultAppModel = newApplication();
                }
                ...
            }
        }
    }
    ...
    return appModel;
}
```

<font style="color:#080808;background-color:#ffffff;"> 接下来可以继续看下ApplicationModel的生命周期方法</font>

```java
public class ApplicationModel extends ScopeModel {
    //当前应用级模型下的模块模型
    private final List<ModuleModel> moduleModels = new CopyOnWriteArrayList<>();
    ...
    //服务存储库
    private volatile ServiceRepository serviceRepository;
    //应用发布器
    private volatile ApplicationDeployer deployer;
    ...
    private final FrameworkModel frameworkModel;
 

    protected ApplicationModel(FrameworkModel frameworkModel) {
        this(frameworkModel, false);
    }

    protected ApplicationModel(FrameworkModel frameworkModel, boolean isInternal) {
        super(frameworkModel, ExtensionScope.APPLICATION, isInternal);
        ...
        this.frameworkModel = frameworkModel;
        ...
        //初始化方法，调用父类ScopeModel中的initialize进行初始化
        initialize();
        //创建一个内部的模块模型，用于初始化内置服务比如：MetadataService
        this.internalModule = new ModuleModel(this, true);
        //应用级服务存储管理
        this.serviceRepository = new ServiceRepository(this);
        
        //应用初始化监听器扩展，初始化ApplicationModel时调用其init方法
        ExtensionLoader<ApplicationInitListener> extensionLoader =
        this.getExtensionLoader(ApplicationInitListener.class);
        Set<String> listenerNames = extensionLoader.getSupportedExtensions();
        for (String listenerName : listenerNames) {
            extensionLoader.getExtension(listenerName).init();
        }
        
        //应用生命周期ApplicationExt扩展，调用其初始化方法initialize
        initApplicationExts();
        
        //域模型初始化扩展，在初始化ApplicationModel时候可以进行初始化或者向应用级ApplicationModel容器中注册对象
        ExtensionLoader<ScopeModelInitializer> initializerExtensionLoader =
        this.getExtensionLoader(ScopeModelInitializer.class);
        Set<ScopeModelInitializer> initializers = initializerExtensionLoader.getSupportedExtensionInstances();
        //遍历域模型初始化列表，调用其initializeApplicationModel方法
        for (ScopeModelInitializer initializer : initializers) {
            initializer.initializeApplicationModel(this);
        }
        ... 
    }

     
    private void initApplicationExts() {
        //应用生命周期ApplicationExt扩展，调用其初始化方法initialize
        //当前版本有两个分别是ConfigManager、Environment
        Set<ApplicationExt> exts = this.getExtensionLoader(ApplicationExt.class).getSupportedExtensionInstances();
        for (ApplicationExt ext : exts) {
            ext.initialize();
        }
    }
}

```

<font style="color:#080808;background-color:#ffffff;"> </font>

<font style="color:#080808;background-color:#ffffff;">上面代码中主要列举了ApplicationModel初始化的构造器， 在这个方法中有几个地方需要我们重点关注的，第一个地方是</font>`<font style="color:#080808;background-color:#ffffff;">initialize()</font>`<font style="color:#080808;background-color:#ffffff;">方法，这个方法同样来源于父类型ScopeModel，主要用于初始化Bean容器管理的实例，具体实现就在后面的ScopeModel初始化的章节来说明。</font>

<font style="color:#080808;background-color:#ffffff;">然后就是</font>`<font style="color:#080808;background-color:#ffffff;">initApplicationExts</font>`<font style="color:#080808;background-color:#ffffff;">方法，这个方法中有两个应用程序相关的扩展分别是ConfigManager、Environment，这两个类型一个用来管理配置，一个用来处理配置来源，可以看到关于配置的问题都可以找通过这两个类型来分析，关于配置相关的分析也可以详细看后面章节的深入分析。</font>

<font style="color:#080808;background-color:#ffffff;">另外一个比较重要的生命周期调用就是获取ScopeModelInitializer实例来调用其生命周期方法</font>`<font style="color:#080808;background-color:#ffffff;">initializeApplicationModel</font>`<font style="color:#080808;background-color:#ffffff;">，具体有哪些初始化的逻辑会在应用级模型初始化的时候进行初始化呢，下面可以通过一个表格来了解下这些初始化类型，方便后面用到时候知道所在类型是何时进行初始化的也方便在用到的时候知道应该从哪个容器中获取：</font>

| **<font style="color:rgb(51, 51, 51);">域模型初始化扩展</font>** | **初始化涉及到的类型** | **说明** |
| :--- | :--- | :--- |
| <font style="color:#080808;background-color:#ffffff;">AdaptiveScopeModelInitializer</font> | <font style="color:#080808;background-color:#ffffff;">AdaptiveMetrics</font> | <font style="color:rgb(51, 51, 51);">自适应负载均衡算法的指标统计</font> |
| <font style="color:#080808;background-color:#ffffff;">ClusterScopeModelInitializer</font> | <font style="color:#080808;background-color:#ffffff;">ClusterUtils</font> | <font style="color:rgb(51, 51, 51);">处理</font><font style="color:#080808;background-color:#ffffff;">mergeUrl的工具类</font> |
| <font style="color:#080808;background-color:#ffffff;">MergeableClusterScopeModelInitializer</font> | <font style="color:#080808;background-color:#ffffff;">MergerFactory</font> | <font style="color:rgb(51, 51, 51);">类型合并器工程</font> |
| <font style="color:#080808;background-color:#ffffff;">CommonScopeModelInitializer</font><br/> | <font style="color:#080808;background-color:#ffffff;">ShutdownHookCallbacks</font> | <font style="color:rgb(51, 51, 51);">关闭回调容器</font> |
| | <font style="color:#080808;background-color:#ffffff;">FrameworkStatusReportService</font> | <font style="color:rgb(51, 51, 51);">框架状态数据报告服务 </font> |
| | <font style="color:#080808;background-color:#ffffff;">ConfigurationCache</font> | <font style="color:rgb(51, 51, 51);">配置缓存</font> |
| <font style="color:#080808;background-color:#ffffff;">ConfigScopeModelInitializer</font><br/><font style="color:#080808;background-color:#ffffff;"></font> | <font style="color:#080808;background-color:#ffffff;">DefaultConfigValidator</font> | <font style="color:rgb(51, 51, 51);">默认的配置验证器</font> |
| | <font style="color:#080808;background-color:#ffffff;">DefaultApplicationDeployer</font> | **<font style="color:rgb(51, 51, 51);">默认的应用发布器</font>** |
| <font style="color:#080808;background-color:#ffffff;">MetadataScopeModelInitializer</font> | <font style="color:#080808;background-color:#ffffff;">MetadataReportInstance</font> | <font style="color:rgb(51, 51, 51);">元数据实例，用于缓存元数据</font> |
| <font style="color:#080808;background-color:#ffffff;">MetricsScopeModelInitializer</font> | <font style="color:#080808;background-color:#ffffff;">MetricsDispatcher</font> | <font style="color:rgb(51, 51, 51);">指标事件分发器</font> |
| <font style="color:#080808;background-color:#ffffff;">RegistryScopeModelInitializer</font> | <font style="color:#080808;background-color:#ffffff;">RegistryManager</font> | <font style="color:rgb(51, 51, 51);">注册中心管理器</font> |
| | <font style="color:#080808;background-color:#ffffff;">MetadataServiceDelegation</font> | <font style="color:rgb(51, 51, 51);">提供远程RPC服务</font> |


<font style="color:#080808;background-color:#ffffff;">可以看到应用级模型初始化的时候同样存在很多应用级的类型需要进行初始化，对于这些类型的初始化暂时可以简单认识一下，方便看到时候有所了解，到这里应用级模型初始化已完成。</font>

**<font style="color:#080808;background-color:#ffffff;">ModuleModel的初始化</font>**

<font style="color:#080808;background-color:#ffffff;">在前面示例中可以看到应用级模型初始化的时候创建了一个内部使用的模块模型对象internalModule如下所示：</font>

```java
//创建一个内部的模块模型，用于初始化内置服务比如：MetadataService
this.internalModule = new ModuleModel(this, true);
```

内部模块模型可用于管理一些内部的服务，比如Dubbo3中提供者为消费者提供的元数据服务MetadataService，以这个调用为例，我们来看下模块模型的生命周期代码：

```java
/**
 * 服务模块的模块模型
 */
public class ModuleModel extends ScopeModel {

    private final ApplicationModel applicationModel;
    private volatile ModuleServiceRepository serviceRepository;
    ...
    private volatile ModuleDeployer deployer;

    ...
    protected ModuleModel(ApplicationModel applicationModel, boolean isInternal) {
        super(applicationModel, ExtensionScope.MODULE, isInternal);
        ...
        this.applicationModel = applicationModel;
        ...
        //初始化方法，调用父类ScopeModel中的initialize进行初始化
        initialize();

        //模块级服务存储管理
        this.serviceRepository = new ModuleServiceRepository(this);

        //应用生命周期ModuleExt扩展，调用其初始化方法initialize
        initModuleExt();

        //域模型初始化扩展，在初始化ModuleModel时候可以进行初始化或者向模块级ModuleModelModel容器中注册对象
        ExtensionLoader<ScopeModelInitializer> initializerExtensionLoader =
        this.getExtensionLoader(ScopeModelInitializer.class);
        Set<ScopeModelInitializer> initializers = initializerExtensionLoader.getSupportedExtensionInstances();
        //遍历域模型初始化列表，调用其initializeApplicationModel方法
        for (ScopeModelInitializer initializer : initializers) {
            initializer.initializeModuleModel(this);
        }
        //...
        // notify application check state
        ApplicationDeployer applicationDeployer = applicationModel.getDeployer();
        if (applicationDeployer != null) {
            applicationDeployer.notifyModuleChanged(this, DeployState.PENDING);
        }
    }

    // already synchronized in constructor
    private void initModuleExt() {
        //模块生命周期ModuleExt扩展，调用其初始化方法initialize
        //当前版本有两个分别是ModuleConfigManager、ModuleEnvironment
        Set<ModuleExt> exts = this.getExtensionLoader(ModuleExt.class).getSupportedExtensionInstances();
        for (ModuleExt ext : exts) {
            ext.initialize();
        }
    }

    ...
}
```

 <font style="color:#080808;background-color:#ffffff;">上面代码中主要列举了ModuleModel初始化的构造器，在这个方法中同样有几个地方需要我们重点关注的，第一个地方是</font>`<font style="color:#080808;background-color:#ffffff;">initialize()</font>`<font style="color:#080808;background-color:#ffffff;">方法，这个方法同样来源于父类型ScopeModel，主要用于初始化Bean容器管理的实例，具体实现就在后面的ScopeModel初始化的章节来说明。</font>

<font style="color:#080808;background-color:#ffffff;">然后就是 </font>`<font style="color:#080808;background-color:#ffffff;">initModuleExt</font>`<font style="color:#080808;background-color:#ffffff;"> 方法，这个方法中有两个应用程序相关的扩展分别是ModuleConfigManager、ModuleEnvironment，这两个类型一个用来管理配置，一个用来处理配置来源，可以看到关于模块级的配置的问题都可以通过这两个类型来分析，关于配置相关的分析也可以详细看后面章节的深入分析。</font>

<font style="color:#080808;background-color:#ffffff;">另外一个比较重要的生命周期调用就是获取ScopeModelInitializer实例来调用其生命周期方法</font>`<font style="color:#080808;background-color:#ffffff;">initializeModuleModel</font>`<font style="color:#080808;background-color:#ffffff;">，具体有哪些初始化的逻辑会在应用级模型初始化的时候进行初始化呢，下面可以通过一个表格来了解下这些初始化类型，方便后面用到时候知道所在类型是何时进行初始化的也方便在用到的时候知道应该从哪个容器中获取：</font>

| **<font style="color:rgb(51, 51, 51);">域模型初始化扩展</font>** | **初始化涉及到的类型** | **说明** |
| :--- | :--- | :--- |
| <font style="color:#080808;background-color:#ffffff;">CommonScopeModelInitializer</font> | <font style="color:#080808;background-color:#ffffff;">ConfigurationCache</font> | <font style="color:rgb(51, 51, 51);">配置缓存</font> |
| | <font style="color:#080808;background-color:#ffffff;">SerializeSecurityConfigurator</font> | <font style="color:rgb(51, 51, 51);">序列化安全配置</font> |
| <font style="color:rgb(51, 51, 51);">ConfigScopeModelInitializer</font> | <font style="color:rgb(51, 51, 51);">DefaultModuleDeployer</font> | <font style="color:rgb(51, 51, 51);">导出或引用服务</font> |
| <font style="color:rgb(51, 51, 51);">MeshScopeModelInitializer</font> | <font style="color:rgb(51, 51, 51);">MeshRuleManager</font> | <font style="color:rgb(51, 51, 51);">Mesh规则管理</font> |


<font style="color:rgb(51, 51, 51);">前面了解了三大领域模型初始化逻辑，在它们初始化的时候都有调用到ScopeModel的构造器和initialize初始化方法，接下来就来看下，这两个方法，对应ScopeModel的初始化代码如下所示：</font>

```java
public abstract class ScopeModel implements ExtensionAccessor {
    ...
    //父级域模型
    private final ScopeModel parent;
    //域，目前支持FRAMEWORK、APPLICATION、MODULE、SELF
    private final ExtensionScope scope;
    //扩展访问器
    private volatile ExtensionDirector extensionDirector;
    //Bean工厂用于创建和管理Dubbo的Bean对象
    private volatile ScopeBeanFactory beanFactory;
    ...

    protected ScopeModel(ScopeModel parent, ExtensionScope scope, boolean isInternal) {
        this.parent = parent;
        this.scope = scope;
        this.internalScope = isInternal;
    }


    protected void initialize() {
        ...
        //扩展访问器，ExtensionLoader的管理器对象创建
        this.extensionDirector =
        new ExtensionDirector(parent != null ? parent.getExtensionDirector() : null, scope, this);
        //扩展的生命周期方法，注入扩展之前和之后触发对应方法
        this.extensionDirector.addExtensionPostProcessor(new ScopeModelAwareExtensionProcessor(this));
        //Bean工厂用于创建和管理Dubbo的Bean对象
        this.beanFactory = new ScopeBeanFactory(parent != null ? parent.getBeanFactory() : null, extensionDirector);
        ...
    }
    ...
}
```

<font style="color:rgb(51, 51, 51);">可以看到在 </font>`<font style="color:rgb(51, 51, 51);background-color:rgb(243, 244, 244);">initialize</font>`<font style="color:rgb(51, 51, 51);"> 方法中主要初始化了两大对象管理容器：扩展访问器ExtensionDirector和域Bean工厂对象ScopeBeanFactory，这两个对象一个用来管理扩展，一个用来管理内部共享对象，可以看到域模型对象的一大核心就是统一对象的管理。</font>

**ExtensionDirector**

ExtensionDirector是一个作用域扩展加载器管理器。ExtensionDirector支持多个级别，子级可以继承父级的扩展实例。查找和创建扩展实例的方法类似于Java类加载器。

<font style="color:rgb(51, 51, 51);">了解了三大域模型的初始化与对象容器化的管理，接下来可以通过提供者，消费者的启动逻辑来看下服务的生命周期。</font>

