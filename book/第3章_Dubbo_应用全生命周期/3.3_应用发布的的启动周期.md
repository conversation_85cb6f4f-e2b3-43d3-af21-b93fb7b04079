### Dubbo在Spring框架中的初始化
在Dubbo中提供了<font style="color:#080808;background-color:#ffffff;">ReferenceAnnotationBeanPostProcessor来实现BeanFactoryPostProcessor接口</font>

 

### <font style="color:rgb(51, 51, 51);">应用启动器</font>
<font style="color:rgb(55, 65, 81);">DubboBootstrap 实例是代表一个 Dubbo 应用的关键组件，也是我们当前 Dubbo示例应用的启动入口。在此基础上，我们可以配置协议（protocol）、服务（service）、注册中心（registry）等，从而实现服务注册、连接注册中心等功能，可以方便的进行应用配置。</font>

<font style="color:rgb(55, 65, 81);">将 DubboBootstrap.start() 用作应用的集中启动入口。这样设计的好处在于，Dubbo 可以提供一个统一的启动方式。不过，为了满足某些场景需求，即在进程启动后动态地发布一些服务，我们还可以直接调用 ServiceConfig.export() 或 ReferenceConfig.refer() 方法进行服务发布。这种灵活性为开发者在运行时管理服务提供了更多的选择。</font>

<font style="color:rgb(55, 65, 81);"></font>

<font style="color:rgb(51, 51, 51);">回到我们最初的案例来看DubboBootstrap的源码，在案例中先获取启动器对象，然后对服务进行配置，最后通过调用start方法来启动服务，如下所示：</font>

```java
DubboBootstrap.getInstance()
//启动服务
.start()
.await();
```

<font style="color:rgb(51, 51, 51);">我们从DubboBootstrap的start方法开始，如下所示：</font>

```java
public DubboBootstrap start(boolean wait) {
    Future future = applicationDeployer.start();
    ...
    return this;
}
```

启动器核心的逻辑是调用应用程序发布器进行启动。

### <font style="color:rgb(51, 51, 51);">默认的应用发布器</font>
<font style="color:rgb(51, 51, 51);">DefaultApplicationDeployer 类的作用是初始化和管理 Dubbo 应用程序的部署过程。它继承自 AbstractDeployer 并实现了 ApplicationDeployer 接口。主要职责包括：  </font>

+ <font style="color:rgb(51, 51, 51);">初始化应用程序配置和环境。</font>
+ <font style="color:rgb(51, 51, 51);">启动和管理应用程序模块的部署。</font>
+ <font style="color:rgb(51, 51, 51);">注册和销毁服务实例。</font>
+ <font style="color:rgb(51, 51, 51);">管理配置中心和元数据中心。</font>
+ <font style="color:rgb(51, 51, 51);">处理应用程序的启动和停止生命周期事件。</font>
+ <font style="color:rgb(51, 51, 51);">确保应用程序在启动和停止过程中正确地初始化和清理资源。</font>

<font style="color:rgb(51, 51, 51);">DefaultApplicationDeployer简化了Dubbo 应用程序的初始化、配置、部署和生命周期管理过程。</font>

<font style="color:rgb(51, 51, 51);">接下来就来深入了解下DefaultApplicationDeployer的start源码。    </font>

<font style="color:rgb(51, 51, 51);">DefaultApplicationDeployer通过调用start方法来启动应用核心逻辑主要包含如下图所示：</font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1735915676320-3d7a6c78-43d6-4eb2-9480-88d8cd0bb555.png)

关于start方法源码如下：

```java
@Override
public Future start() {
        ...
        // pending -> starting : first start app
        // started -> starting : re-start app
        //将发布状态DeployState设置为STARTING
        onStarting();
        //应用级启动初始化
        initialize();
        //启动应用
        doStart();
}
```

<font style="color:rgb(51, 51, 51);">启动方法比较简单一共包含三个主要的逻辑：</font>

+ <font style="color:rgb(51, 51, 51);">状态机处理</font>
+ <font style="color:rgb(51, 51, 51);">应用初始化</font>
+ <font style="color:rgb(51, 51, 51);">启动应用</font>

**<font style="color:rgb(51, 51, 51);">DefaultApplicationDeployer的initialize方法</font>**

```java
@Override
public void initialize() {
    //注册DubboShutdownHook用于关闭时执行销毁逻辑
    registerShutdownHook();
    //客户端启动配置中心
    startConfigCenter();
    //加载应用配置
    loadApplicationConfigs();
    // @since 2.7.8
    // 客户端启动元数据中心
    startMetadataCenter();
   
    //...
}
```

<font style="color:rgb(51, 51, 51);">默认的应用程序发布器在初始化的时候进行了一些应用级的初始化，其中如下逻辑需要重点关注：</font>

+ <font style="color:rgb(51, 51, 51);">应用关闭回调方法注册</font>
+ <font style="color:rgb(51, 51, 51);">配置中心初始化</font>
+ <font style="color:rgb(51, 51, 51);">应用配置初始化</font>
+ <font style="color:rgb(51, 51, 51);">元数据中心初始化</font>

**<font style="color:rgb(51, 51, 51);">DefaultApplicationDeployer的doStart方法</font>**

```java
private void doStart() {
    startModules();
}

private void startModules() {
     ...
    // 启动挂起的模块（初始的发布器状态为PENDING）
    for (ModuleModel moduleModel : applicationModel.getModuleModels()) {
        if (moduleModel.getDeployer().isPending()) {
            moduleModel.getDeployer().start();
        }
    }
}
```

在doStart方法中通过调用startModules方法来启动当前应用模型下的所有模块模型。

### <font style="color:rgb(51, 51, 51);">默认的模块发布器</font>
`<font style="color:#a31615;">DefaultModuleDeployer</font>`<font style="color:rgb(51, 51, 51);"> 类的作用是管理模块的部署过程，包括初始化、启动、停止和销毁模块。它继承自 </font>`<font style="color:#a31615;">AbstractDeployer</font>`<font style="color:rgb(51, 51, 51);"> 并实现了 </font>`<font style="color:#a31615;">ModuleDeployer</font>`<font style="color:rgb(51, 51, 51);"> 接口。主要功能包括：</font>

+ 初始化模块<font style="color:rgb(51, 51, 51);">：加载模块配置，设置异步导出和引用服务的选项。 </font>
+ 启动模块<font style="color:rgb(51, 51, 51);">：导出服务，引用服务，注册服务到注册中心，并检查引用配置。 </font>
+ 停止模块<font style="color:rgb(51, 51, 51);">：取消服务的导出和引用，注销服务。 </font>
+ 管理模块的生命周期<font style="color:rgb(51, 51, 51);">：处理模块的启动、停止、失败等状态变化，并通知相关的监听器。 </font>

<font style="color:rgb(51, 51, 51);"> </font>`<font style="color:#a31615;">DefaultModuleDeployer</font>`<font style="color:rgb(51, 51, 51);"> 负责管理模块的生命周期，包括初始化、启动、停止和销毁模块内的服务。 </font>

<font style="color:rgb(51, 51, 51);">应用发布器在doStart方法中启动模块发布器，关于模块发布器的内容可以看如下所示</font>**<font style="color:rgb(51, 51, 51);"> </font>**

**<font style="color:rgb(51, 51, 51);">DefaultModuleDeployer的start方法</font>**

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1735915805058-8733cefd-ef4a-4411-b610-ab10b8cdb613.png)

```java
@Override
public Future start() throws IllegalStateException {
    ...
    return startSync();
}
```

**<font style="color:rgb(51, 51, 51);">DefaultModuleDeployer的startSync方法</font>**

```java
private synchronized Future startSync() throws IllegalStateException {
    ...
    
    //提供者导出服务
    exportServices();

    ...

    //消费者引用服务
    referServices();

    //不存在异步导出服务则处理启动完成
    if (asyncExportingFutures.isEmpty() && asyncReferringFutures.isEmpty()) {
        
        // 注册服务
        registerServices();
        ...
    } else {
        frameworkExecutorRepository.getSharedExecutor().submit(() -> {
            ...
            // wait for export finish
            waitExportFinish();

            // wait for refer finish
            waitReferFinish();
            
            // publish module started event
            // 注册服务
            registerServices();
        });
    }
    ...
    return startFuture;
}
```

服务启动的逻辑我们总是在服务发现模型中看到，提供者启动进程、导出服务，注册服务，消费者进行服务的订阅，然后进行RPC调用。

模块发布器的启动方法正是服务启动生命周期的核心部分，模块发布器的在启动时候进行服务的导出、引用、注册等逻辑。先处理服务的导出，再注册服务避免服务启动失败被订阅到而引发异常。

<font style="color:rgb(51, 51, 51);">另外在模块发布器启动的过程中也处理了异步导出/引用服务的逻辑，如果存在异步导出/引用时则使用异步任务来进行等待，待服务导出/引用处理完成之后进行服务注册。</font>

### <font style="color:rgb(51, 51, 51);">启动周期与服务发现的关系</font>
<font style="color:rgb(51, 51, 51);">整体来看Dubbo启动器使用应用发布器和模块发布器启动服务的过程都是围绕服务来进行的，主要包含服务的导出exportServices、服务的引用referServices、服务注册registerServices等，事先了解Dubbo基本的原理更有利于了解源码，接下来可以简单回顾下Dubbo服务发现的过程，主要包含服务启动、导出服务、注册服务信息到注册中心、消费者订阅/被通知服务提供者，如下图所示：</font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1735974786920-ad03e10f-3fa5-4b93-8f8a-3392c6febb39.png)

<font style="color:rgb(51, 51, 51);"></font>

