<font style="color:rgb(51, 51, 51);">作为云原生时代的里程碑式升级，Apache Dubbo 3.x 在全面强化服务治理、性能优化等核心能力的同时，更以明确的云原生技术演进路线指引着整个生态体系的进化方向。云原生技术体系涵盖微服务架构、容器化编排、持续交付、自动化运维等核心技术要素，要求应用系统在开发范式、交付流程和运行环境等维度实现全面进化。为帮助开发者构建符合云原生标准的企业级服务架构，Dubbo 3.x 重点突破传统微服务的边界约束，通过服务网格化治理、应用级服务发现、Kubernetes原生集成等创新设计，实现从开发调试到生产部署的全链路云原生能力支撑。其中，对GraalVM原生镜像编译能力的支持，是Dubbo拥抱云原生进程中非常关键的特性之一。</font>

<font style="color:rgb(51, 51, 51);">Dubbo对GraalVM Native Image技术的集成演进可分为两个重要阶段：</font>

<font style="color:rgb(51, 51, 51);">一、实验性支持阶段（2021年6月，3.0版本）</font>

<font style="color:rgb(51, 51, 51);">Dubbo 3.0首次尝试集成GraalVM Native Image技术，实现了基础支持能力，但在实际应用中存在以下关键挑战：</font>

1. <font style="color:rgb(51, 51, 51);">开发体验问题</font>
    - <font style="color:rgb(51, 51, 51);">代码生成侵入性：通过独立工具生成的SPI接口Adaptive Source code直接写入应用工程目录，而非标准的target编译目录，对项目源码结构造成侵入</font>
    - <font style="color:rgb(51, 51, 51);">配置维护困难：需要手动为所有SPI接口生成适配代码，增加额外开发步骤</font>
2. <font style="color:rgb(51, 51, 51);">维护成本问题</font>
    - <font style="color:rgb(51, 51, 51);">元数据全量维护：需持续维护完整的Reachability Metadata配置，新增功能时必须同步更新元数据配置</font>
    - <font style="color:rgb(51, 51, 51);">配置遗漏风险：开发者若未及时更新元数据，可能导致编译失败或功能缺失</font>
1. <font style="color:rgb(51, 51, 51);">资源效率问题</font>
    - <font style="color:rgb(51, 51, 51);">冗余依赖打包：未实现按需加载机制，即使未使用的组件（如Nacos注册中心）相关元数据仍会被打包</font>
    - <font style="color:rgb(51, 51, 51);">构建效率下降：全量元数据导致编译时间延长，最终二进制文件体积膨胀约30%</font>
1. <font style="color:rgb(51, 51, 51);">场景适配局限</font>
    - <font style="color:rgb(51, 51, 51);">仅支持API方式接入，无法兼容XML/注解等主流配置方式，覆盖不足70%的用户场景</font>

<font style="color:rgb(51, 51, 51);">二、生产级支持阶段（2023年4月，3.2版本）</font><font style="color:rgb(51, 51, 51);">通过架构重构和技术创新，3.2版本实现了以下突破性改进：</font>

1. <font style="color:rgb(51, 51, 51);">智能化构建体系</font>
    - <font style="color:rgb(51, 51, 51);">SPI接口自动识别：编译期动态扫描并生成Adaptive Source code，输出至标准target目录</font>
    - <font style="color:rgb(51, 51, 51);">元数据自动生成：通过编译器插件自动创建Reachability Metadata，消除人工维护成本</font>
1. <font style="color:rgb(51, 51, 51);">工程化能力升级</font>
    - <font style="color:rgb(51, 51, 51);">构建产物优化：引入按需加载机制，最终包体缩减40%，编译速度提升50%</font>
    - <font style="color:rgb(51, 51, 51);">多接入方式支持：完整兼容API/XML/注解等配置方式，覆盖率提升至100%</font>
1. <font style="color:rgb(51, 51, 51);">统一工具链整合</font>
    - <font style="color:rgb(51, 51, 51);">推出dubbo-maven-plugin：整合原生编译能力，作为框架唯一官方Maven插件</font>
    - <font style="color:rgb(51, 51, 51);">降低心智负担：通过标准化插件收敛构建流程，开发者学习成本降低60%</font>

<font style="color:rgb(51, 51, 51);">Dubbo在深度集成GraalVM Native Image技术的过程中，还支持了全平台编译支持，比如支持了主流操作系统覆盖：完整支持Linux/macOS/Windows三大平台的Native Image构建，架构兼容性保障：针对x86_64和ARM64架构提供差异化编译策略。并且对Dubbo关键能力的原生适配程度也越发完善，实现核心协议栈的深度兼容，第一是协议层全面支持：Dubbo协议与Triple协议均通过Native Image验证；第二是高级功能可用性保障：包括服务注册发现、负载均衡、集群容错等机制在AOT模式下完整可用。版本迭代对比表明，3.2版本通过构建时自动化处理、智能元数据管理和统一工具链设计，实现了Native Image支持从实验特性到生产就绪的跨越式发展，为云原生场景提供开箱即用的解决方案。</font>

# <font style="color:rgb(51, 51, 51);">12.3.1 GraalVM Native Image诞生的背景</font>
<font style="color:rgb(51, 51, 51);">在云原生技术范式主导的数字化转型浪潮中，现代应用架构演进呈现三大核心特征：</font>

**<font style="color:rgb(51, 51, 51);">1. 基础设施弹性化</font>**

<font style="color:rgb(51, 51, 51);">云原生架构通过声明式API和自动化编排引擎，构建起智能弹性基座，并基于标准化容器镜像的实例可快速实现横向扩展，应用能够借助这些云计算的基础设施，在云上快速、轻松且高效地做到弹性，无状态服务更是可依据流量指标进行毫秒级实例扩缩，这种动态资源调度能力不仅支撑突发流量洪峰，更能实现闲置资源精准回收，使资源利用率较传统架构提升数倍以上。</font>

**<font style="color:rgb(51, 51, 51);">2. 资源管理原子化</font>**<font style="color:rgb(51, 51, 51);">容器化技术将计算资源解耦为细粒度调度单元，配合cgroups和namespace实现的强隔离性，使CPU、内存等资源可精确划分至进程级别。这种原子化资源管理范式不仅突破传统虚拟化技术的性能损耗瓶颈，更通过Kubernetes调度器实现跨节点资源碎片整合，构建起多维度的资源利用率优化模型。</font>

**<font style="color:rgb(51, 51, 51);">3. 交付体系敏捷化</font>**<font style="color:rgb(51, 51, 51);">云原生技术栈重构了软件交付的生命周期：基础设施即代码(IaC)实现环境一致性，CI/CD流水线支撑分钟级生产部署，A/B测试、蓝绿发布等策略保障业务连续性。开发者通过声明式配置即可完成从代码提交到灰度上线的全流程，应用的部署更加容易，应用开发更加敏捷。</font>

<font style="color:rgb(51, 51, 51);">在这个云原生盛行的时代，用Dubbo构建云原生应用，让 Dubbo 应用能够充分利用云技术的弹性和分布式特性，是 Dubbo 在3.x版本首要的思考和规划方向。Dubbo提供了多种编程语言的SDK，包括Java、Golang、Python等编程语言，而其中 Java 的用户依旧占据着较高的比例。在云原生架构持续演进的技术图景中，Java技术栈正面临与生俱来的架构性挑战。这些根植于JVM运行机制的设计哲学，在追求极致弹性、瞬时扩展的云计算范式下愈发凸显出适配困境，下面四个是比较重要的原因：</font>

1. <font style="color:rgb(51, 51, 51);">冷启动延迟困境：JVM初始化需经历类加载、字节码验证、JIT编译等多阶段启动链，导致应用启动耗时普遍达到秒级（典型Spring Boot应用约15-30秒）。这种冷启动延迟与Kubernetes Pod快速弹性伸缩机制形成显著冲突，当突发流量激增时，传统扩缩策略难以满足分钟级千实例扩容的云原生弹性要求。</font>
2. <font style="color:rgb(51, 51, 51);">性能预热悖论：即使完成启动流程，JVM仍需通过热点代码检测触发分层编译（C1/C2编译器），此阶段吞吐量仅达峰值性能的30-60%。在服务网格架构中，未配置预热策略的Pod若直接接入流量洪峰，极易触发级联超时故障。这种"启动即高负载"的现状，严重制约了自动扩缩容系统的无损发布能力。对于没有做预热处理，并且对延时又比较敏感的应用，会导致其在发布时有一定的接口超时情况，这就会导致无法达到无损地极致弹性扩容的效果。</font>
3. <font style="color:rgb(51, 51, 51);">资源利用效能瓶颈：Java 应用的内存占用和CPU使用率相对较高，典型Java进程内存占用量常达GB级（堆内存+元空间+JIT代码缓存），且JIT编译带来持续CPU开销。当资源占用过高时，不得不为 Java应用提供更高规格的实例，但是在Kubernetes资源原子化调度机制下，切分大规格的实例，会导致切分后造成的碎片更大，从而导致资源的浪费。比如12G 能拆分出3个需要4G的应用实例，但是却只能提供给1个需要8G的应用实例，并且会造成4G的碎片。</font>
4. <font style="color:rgb(51, 51, 51);">环境依赖复杂性：Java应用的启动需要有JDK环境，造成了Java的应用程序构建和执行变得更加繁琐，而且多版本JDK环境维护成本激增 ，除此之外，它还会导致容器镜像体积膨胀。</font>

<font style="color:rgb(51, 51, 51);">拿Serverless和Faas场景举例，由于要做到秒级弹性扩容，除了容器调度和新的Pod创建的时间损耗以外，镜像下载的时间、应用冷启动耗时、以及应用服务预热所需的耗时，都是影响弹性扩容速度的因素。而Java 与别的编程语言相比，无论是在冷启动方面还是生成的产物对镜像大小的影响方面，都处于一定的劣势。正因为这些系统性矛盾的影响越来越大，才推动了GraalVM Native Image等云原生编译技术的崛起，通过剥离JVM运行时依赖、实现毫秒级冷启动等技术路径，重构Java在云原生时代的价值坐标系。</font>

# <font style="color:rgb(51, 51, 51);">12.3.2 GraalVM Native Image介绍</font>
<font style="color:rgb(51, 51, 51);">GraalVM 是由 Oracle 开发的高性能跨语言运行时环境，旨在突破传统虚拟机的限制，支持多语言互操作与高效执行。GraalV是OpenJDK的“超集”，它包含了完整的JDK发行版本，意味着在使用新特性的同时，Java应用程序也能够按照原有的形式在Graalvm环境下正常的运行。GraalVM 还包含GraalVM Compiler、Native image、Truffle等组件，它不仅能够运行 Java、Scala、Kotlin 等 JVM 语言，还通过 Truffle 框架无缝集成 JavaScript、Python、Ruby、R 等动态语言，允许不同语言在同一个应用中混合调用，达到提供多语言混编的目的。GraalVM 的核心优势包括：</font>

+ <font style="color:rgb(51, 51, 51);">高性能：通过先进的即时编译（JIT）技术优化代码执行效率，部分场景性能超越传统 JVM。</font>
+ <font style="color:rgb(51, 51, 51);">多语言融合：打破语言边界，支持异构生态的组件直接交互（如 Java 调用 Python 库）。</font>
+ <font style="color:rgb(51, 51, 51);">云原生适配：提供轻量化、低延迟的运行时特性，适合微服务、Serverless 等现代架构。</font>

<font style="color:rgb(51, 51, 51);">GraalVM 通过 Native Image 和封闭世界原则（Closed-World Assumption），为 Java 生态提供了原生编译的新范式，以“牺牲部分动态灵活性”换取“极致性能与资源效率”。尽管需适应静态编译的约束，但其在云原生领域的优势（如秒级冷启动、超低内存开销）使其成为现代化应用的重要技术选项。GraalVM Native Image 是 GraalVM 的颠覆性功能，可将 JVM 应用提前编译（AOT）为独立原生二进制可执行文件（如 Linux/Mac/Windows 可执行程序）。GraalVM Native Image 在构建阶段通过静态分析确定所有可达代码，移除未使用的类、方法或字段，使得编译出的二进制文件产物远小于 Jar包，并且它依赖 Substrate VM 提供必要的运行时服务（如垃圾回收、线程调度），仅保留了最小化功能，让应用程序直接编译为机器码，无需安装 JDK/JRE，依赖的内容完全内嵌，使得应用程序达到脱离JVM运行的目的，除此之外GraalVM 编译出来的二进制可执行文件还具备以下两个优点：</font>

1. <font style="color:rgb(51, 51, 51);">极致冷启动速度：启动时间从秒级缩短至毫秒级（例如 Spring Boot 应用可 0.1 秒启动），适合瞬时扩容场景。</font>
2. <font style="color:rgb(51, 51, 51);">资源占用低：内存占用减少 50%~90%，特别适合容器化部署（缩小镜像体积）和资源受限环境（如边缘设备）。</font>

## <font style="color:rgb(51, 51, 51);">AOT 和 JIT</font>
<font style="color:rgb(51, 51, 51);">GraalVM Native Image 技术 是 AOT（Ahead Of Time）技术在 Java 生态中的一种具体实现，在传统的Java开发过程中，我们采用的都是JIT，JIT即为将我们编写的Java源代码编译为.class文件，并在运行期间将JVM认为是热点的字节码转化为机器码，加快程序运行速度。而 AOT技术 则是在编译期间就将字节码转化为了机器码，它将不再会有运行期还在持续将热点的字节码转化为机器码的操作。</font>

<font style="color:rgb(51, 51, 51);">Java应用完整的生命周期包含五个阶段：</font>

1. <font style="color:rgb(51, 51, 51);">JVM的启动初始化阶段</font>
2. <font style="color:rgb(51, 51, 51);">Java Main函数的启动阶段</font>
3. <font style="color:rgb(51, 51, 51);">应用预热阶段</font>
4. <font style="color:rgb(51, 51, 51);">应用稳定运行的阶段</font>
5. <font style="color:rgb(51, 51, 51);">最后是应用销毁阶段，至此整个Java应用的生命周期完结。</font>

![](https://cdn.nlark.com/yuque/0/2025/png/337436/1741527537189-7099bc3d-0c32-4107-85b6-78760c0edd84.png)

<font style="color:rgb(51, 51, 51);">可以看到在Java的整个生命周期中，如果是传统的JIT模式，从启动到达到应用平稳的阶段，需要经过JVM的初始化，类加载，解释器执行、即时编译、GC，并且即使到达了应用的稳定阶段，JIT、解释器依旧还在运行。而对于GraalVM中的AOT技术而言，首先省去了JVM的启动，也就是上图红色区域的时间，第二是省去看解释器和编译器所占用的时间。</font>

### <font style="color:rgb(51, 51, 51);">Closed-World Assumption</font>
<font style="color:rgb(51, 51, 51);">Closed-World Assumption（封闭世界的原则）是 GraalVM Native Image 的核心约束，要求编译时必须明确所有可能执行的代码路径，禁止运行时动态加载未预见的类或资源。其设计逻辑与影响主要包括静态可达性分析和动态特性的限制两个方面：</font>

1. <font style="color:rgb(51, 51, 51);">静态可达性分析</font>
    - <font style="color:rgb(51, 51, 51);">编译器从入口类（如 Main方法）出发，递归扫描所有可能调用的代码分支。</font>
    - <font style="color:rgb(51, 51, 51);">未被显式引用的代码会被剔除，例如通过反射调用的方法需手动配置。</font>
2. <font style="color:rgb(51, 51, 51);">动态特性的限制</font>
    - <font style="color:rgb(51, 51, 51);">反射/动态代理/资源加载：需提前通过配置文件（JSON）或 Agent 工具声明，否则运行时抛出 </font>`<font style="color:rgb(51, 51, 51);background-color:rgb(243, 244, 244);">ClassNotFoundException</font>`<font style="color:rgb(51, 51, 51);">。</font>
    - <font style="color:rgb(51, 51, 51);">动态类加载：无法在运行时通过 </font>`<font style="color:rgb(51, 51, 51);background-color:rgb(243, 244, 244);">ClassLoader</font>`<font style="color:rgb(51, 51, 51);"> 加载新类（如热部署、插件化架构需重构）。</font>

# <font style="color:rgb(51, 51, 51);">12.3.3 Dubbo 支持构建GraalVM Native Image的原因</font>
<font style="color:rgb(51, 51, 51);">GraalVM Native Image特性通过AOT（Ahead-of-Time）编译技术，使Dubbo应用能够构建出启动速度更快、内存消耗更低的轻量化原生镜像，完美契合Serverless、FaaS等云原生场景对极致弹性与资源效能的严苛要求，标志着Dubbo在云原生技术栈的深度融合进程中迈出关键一步，GraalVM 为Dubbo 带来了 为以下四个技术红利：</font>

1. **<font style="color:rgb(51, 51, 51);">创新产物形态</font>**<font style="color:rgb(51, 51, 51);">GraalVM为Dubbo应用引入了革命性的产物形态——原生可执行文件（Native Executable），突破了传统Jar包的单一生态格局。相较于需依赖JDK环境运行的Jar包，该形态具备两大核心优势：其一实现零JDK依赖的独立部署能力，其二显著缩减产物体积。这一创新彻底改变了Java应用的交付范式，使Dubbo应用获得与Go/Rust等原生语言同等级别的部署特性。</font>
2. **<font style="color:rgb(51, 51, 51);">启动性能飞跃</font>**<font style="color:rgb(51, 51, 51);">历经十年演进的Dubbo框架在功能丰富化的同时，也面临启动耗时递增的技术债问题。通过GraalVM Native Image技术的深度集成，原生可执行文件成功将启动耗时压缩至传统Jar包的十分之一，实现从秒级到毫秒级的质变突破。这一突破性进展为Dubbo在Serverless等瞬时启动场景的拓展奠定了关键基础。</font>
3. **<font style="color:rgb(51, 51, 51);">瞬时峰值性能</font>**<font style="color:rgb(51, 51, 51);">原生可执行文件采用AOT（Ahead-of-Time）编译技术，彻底规避了JIT（Just-in-Time）预热阶段的性能损耗。实测数据显示，其首请求处理时延较Jar包形态降低达数倍，真正实现"启动即最优"的运行状态。该特性与快速启动能力形成技术共振，大幅提升了Dubbo在FaaS等按需伸缩场景的竞争力。</font>
4. **<font style="color:rgb(51, 51, 51);">内存效能革新</font>**<font style="color:rgb(51, 51, 51);">在云计算降本增效的主流趋势下，原生可执行文件展现出突破性的资源利用率——内存消耗较传统Jar包降低90%。这不仅直接削减了单实例运营成本，更为Java生态开辟了高密度部署的新可能，使Dubbo应用在资源敏感型场景的落地获得显著竞争优势。</font>

# <font style="color:rgb(51, 51, 51);">12.3.4 Dubbo 应用如何构建GraalVM Native Image</font>
<font style="color:rgb(51, 51, 51);">Dubbo的接入方式主要分为Spring 与 非Spring 两种，其中Spring的接入方式包括注解和XML两种接入方式，而非Spring接入方式则为通过Dubbo API接入。所以Dubbo应用在集成GraalVM Native Image 技术时候也分类两类：一类为注解和XML两种接入方式，另一类为API接入方式。</font>

### <font style="color:rgb(51, 51, 51);">注解和XML方式接入</font>
<font style="color:rgb(51, 51, 51);">第一步是为应用配置三个Maven 插件：</font>

1. <font style="color:rgb(51, 51, 51);">spring-boot-maven-plugin： Spring Boot 官方提供的Maven插件，它在 Spring Boot 项目的构建和打包过程中扮演核心角色。它的主要作用是将 Spring Boot 应用打包成 可执行的独立文件，并简化与 Spring Boot 特性相关的构建流程，Spring Boot就是通过该插件来完成Native Executable的编译和构建。</font>
2. <font style="color:rgb(51, 51, 51);">native-maven-plugin：GraalVM官方提供的Maven插件，主要用于加载GraalVM 管理的一些来自社区且已知的Reachability Metadata，帮助应用降低一部分Reachability Metadata处理的负担，它能够在编译阶段将社区管理的Reachability Metadata附加到最终Native Executable构建流程中。</font>
3. <font style="color:rgb(51, 51, 51);">dubbo-maven-plugin：Dubbo 官方提供的 Maven 插件，在AOT 场景下，它提供了Dubbo 中的一些Reachability Metadata自动生成和管理能力以及部分运行时所需的Source code生成逻辑，帮助Dubbo应用能够自动生成相关的Reachability Metadata。</font>

<font style="color:rgb(51, 51, 51);">下面是一个完整的native profile 配置示例。</font>

```plain
<profiles>
    <profile>
        <id>native</id>
        <build>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <configuration>
                        <release>17</release>
                        <fork>true</fork>
                        <verbose>true</verbose>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <executions>
                        <execution>
                            <id>process-aot</id>
                            <goals>
                                <goal>process-aot</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.graalvm.buildtools</groupId>
                    <artifactId>native-maven-plugin</artifactId>
                    <version>${native-maven-plugin.version}</version>
                    <configuration>
                        <classesDirectory>${project.build.outputDirectory}</classesDirectory>
                        <metadataRepository>
                            <enabled>true</enabled>
                        </metadataRepository>
                        <requiredVersion>22.3</requiredVersion>
                    </configuration>
                    <executions>
                        <execution>
                            <id>add-reachability-metadata</id>
                            <goals>
                                <goal>add-reachability-metadata</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.dubbo</groupId>
                    <artifactId>dubbo-maven-plugin</artifactId>
                    <version>${dubbo.version}</version>
                    <configuration>
                        <mainClass>org.apache.dubbo.registry.provider.NativeDemoProviderRegistryApplication</mainClass>
                    </configuration>
                    <executions>
                        <execution>
                            <phase>process-sources</phase>
                            <goals>
                                <goal>dubbo-process-aot</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </build>
    </profile>
</profiles>
```

<font style="color:rgb(51, 51, 51);">第二步是配置所需的依赖：</font>

```plain
<dependency>
    <groupId>org.apache.dubbo</groupId>
    <artifactId>dubbo-native</artifactId>
</dependency>
<dependency>
    <groupId>org.apache.dubbo</groupId>
    <artifactId>dubbo-config-spring6</artifactId>
</dependency>
```

<font style="color:rgb(51, 51, 51);">第三步则是编译和执行Native Executable</font>

<font style="color:rgb(51, 51, 51);">mvn clean install -P native native:compile</font>

<font style="color:rgb(51, 51, 51);">在编译完成后，在工程的根目录下下会生成一个对应的Native Executable，直接执行该Native Executable即可。目前完整的示例代码可以在dubbo-samples项目中被找到。</font>

### <font style="color:rgb(51, 51, 51);">API 接入方式</font>
<font style="color:rgb(51, 51, 51);">API 接入方式的Dubbo应用由于不需要依赖Spring，所以区别于XML 和注解的接入方式的是，在集成GraalVM Native Image时无需增加dubbo-config-spring6依赖和无需增加spring-boot-maven-plugin插件。但是API接入的方式中，用户需要自行配置接口相关的Reachability Metadata。这个的原因是在XML 或者注解的接入方式中，Dubbo是能够通过既定的接入范式来识别用户暴露了哪些接口，或者引用了哪些接口，所以可以为用户自动生成对应的Reachability Metadata，但是由于API接入方式，无法确定暴露和引用了哪些接口，并且也无法在启动阶段确定所有的Dubbo接口，这导致与Closed-World Assumption相悖，所以API接入的Dubbo 应用需要自行配置GraalVM 的 Reachability Metadata ，以保障API暴露的接口和引用的接口能够在Native Executable编译阶段被识别且被打包进入Native Executable中。</font>

# <font style="color:rgb(51, 51, 51);">12.3.5 Dubbo 集成 GraalVM Native Image的原理</font>
<font style="color:rgb(51, 51, 51);">在了解Dubbo 集成 GraalVM Native Image的原理之前，首先需要了解 GraalVM Native Image 的Reachability Metadata，以便于更好地了解Dubbo集成的方案。GraalVM Native Image 的 Reachability Metadata（可达性元数据） 是一种关键配置机制，用于帮助编译器在构建原生应用时，精确识别哪些代码元素（如类、方法、字段）需要在运行时保留。由于 Native Image 通过静态分析提前编译 Java 应用，也就是需要依赖于能够“看到全部的字节码”才能正确工作，传统的动态特性（如反射、动态代理、JNI 或资源加载）可能无法被编译器自动追踪，也就导致这些特性都无法再Native 场景下使用。在Java 开发过程中，这些Java的动态能力被运用在各种场景中，它们早已经是Java开发者非常熟练的编码手段。所以GraalVM 同样也考虑到了这个情况。在既不打破“封闭时间假设”原则的前提下，通过Reachability Metadata来解决这类问题。既然需要在编译器中能够确定所有的字节码和资源，那么就让开发者在编码阶段就确定这些元数据信息。Reachability Metadata 通过显式声明这些动态访问的元素，确保它们在编译阶段被正确包含，从而避免运行时因代码缺失导致的错误。</font>

<font style="color:rgb(51, 51, 51);">对于开发者比较关心的问题就是，如何完整地获取并提供自身应用所需的Reachability Metadata，从而达到Native Executable正确构建和执行的目的。GraalVM提供了两种手段，第一种就是GraalVM提供了Tracing Agent，来辅助开发者在运行时采集对应的Reachability Metadata，开发者可以使用该工具在应用测试运行期间自动捕获反射、JNI 等动态操作，生成对应的元数据配置文件。但是通过Tracing Agent采集的元数据并不能保障能够采集完整，原因是Tracing Agent 只跟踪和采集执行的代码，而程序输入没有覆盖的代码路径，将无法采集到。Graalvm 官网也建议采集后还需要手动check 元数据。第二种是GraalVM 社区维护了常见库的共享元数据库，也就是Reachability Metadata Repository，减少重复的配置工作。Java发展这么多年，诞生出了非常多的组件库，业务开发可以非常轻松地使用这些组件来完成业务功能。纯粹的业务逻辑运用到Java 动态语言特性的场景并不多，反而是这些组件库使用这些特性更加频繁，所以GraalVM 提供了这个Reachability Metadata Repository，吸纳来自不同的组件的Reachability Metadata。举个例子，Netty是使用较为频繁的网络通信框架，Netty内部用到了反射。而在这个仓库内，能够找到Netty的Reachability Metadata。那么这个仓库内的Reachability Metadata如何才能被应用使用，答案是使用Graalvm 官方提供的native-maven-plugin。</font>

<font style="color:rgb(51, 51, 51);">Reachability Metadata目前主要用到六种，分别是：</font>

1. <font style="color:rgb(51, 51, 51);">JNI Metadata：JNI（Java Native Interface）允许 Java 代码调用本地（Native）方法（如 C/C++ 编写的代码），GraalVM 原生镜像需要提前知道哪些 Java 方法有对应的本地实现，否则这些方法可能被编译器优化掉，导致运行时链接失败。开发者可以再在 </font>`<font style="color:rgb(51, 51, 51);background-color:rgb(243, 244, 244);">jni-config.json</font>`<font style="color:rgb(51, 51, 51);"> 中声明需要保留的 JNI 方法和类，或者使用 GraalVM 提供的注解（如 </font>`<font style="color:rgb(51, 51, 51);background-color:rgb(243, 244, 244);">@JNI</font>`<font style="color:rgb(51, 51, 51);">）标记需要保留的方法。</font>
2. <font style="color:rgb(51, 51, 51);">Resource Metadata：Java 应用通常通过 </font>`<font style="color:rgb(51, 51, 51);background-color:rgb(243, 244, 244);">ClassLoader.getResource()</font>`<font style="color:rgb(51, 51, 51);"> 访问资源文件（如配置文件、图片、XML 等）。GraalVM 默认不会将所有资源打包进镜像，需显式指定需要包含的资源路径。开发者可以在 </font>`<font style="color:rgb(51, 51, 51);background-color:rgb(243, 244, 244);">resource-config.json</font>`<font style="color:rgb(51, 51, 51);"> 中定义资源路径的正则表达式匹配规则，或者使用构建参数 </font>`<font style="color:rgb(51, 51, 51);background-color:rgb(243, 244, 244);">-H:IncludeResources</font>`<font style="color:rgb(51, 51, 51);"> 直接指定资源路径。</font>
3. <font style="color:rgb(51, 51, 51);">Dynamic Proxy Metadata：Java 动态代理（</font>`<font style="color:rgb(51, 51, 51);background-color:rgb(243, 244, 244);">Proxy.newProxyInstance()</font>`<font style="color:rgb(51, 51, 51);">）会在运行时生成代理类，但 GraalVM 需在编译时预生成这些代理类。需提前声明哪些接口需要代理。开发者可以在 </font>`<font style="color:rgb(51, 51, 51);background-color:rgb(243, 244, 244);">proxy-config.json</font>`<font style="color:rgb(51, 51, 51);"> 中列出接口全限定名。</font>
4. <font style="color:rgb(51, 51, 51);">Serialization Metadata：Java 序列化（如 </font>`<font style="color:rgb(51, 51, 51);background-color:rgb(243, 244, 244);">ObjectInputStream</font>`<font style="color:rgb(51, 51, 51);">）依赖运行时动态查找类，但 GraalVM 需提前知道哪些类会被序列化/反序列化，以保留相关元数据。开发者需要在 </font>`<font style="color:rgb(51, 51, 51);background-color:rgb(243, 244, 244);">serialization-config.json</font>`<font style="color:rgb(51, 51, 51);"> 中声明可序列化的类及其构造函数。</font>
5. <font style="color:rgb(51, 51, 51);">Reflection Metadata：反射（如 </font>`<font style="color:rgb(51, 51, 51);background-color:rgb(243, 244, 244);">Class.forName()</font>`<font style="color:rgb(51, 51, 51);">、</font>`<font style="color:rgb(51, 51, 51);background-color:rgb(243, 244, 244);">Method.invoke()</font>`<font style="color:rgb(51, 51, 51);">）是动态行为，GraalVM 无法通过静态分析完全推断哪些类/方法会被反射调用，需显式配置。开发者可以在 </font>`<font style="color:rgb(51, 51, 51);background-color:rgb(243, 244, 244);">reflect-config.json</font>`<font style="color:rgb(51, 51, 51);"> 中列出类及其需反射访问的字段/方法。</font>
6. <font style="color:rgb(51, 51, 51);">Predefined Classes Metadata：某些类需要在编译时（Build Time）预先初始化，以提升启动性能。但需确保这些类的初始化不依赖运行时环境（如文件系统、网络）。开发者可以在 </font>`<font style="color:rgb(51, 51, 51);background-color:rgb(243, 244, 244);">native-image.properties</font>`<font style="color:rgb(51, 51, 51);"> 中指定 </font>`<font style="color:rgb(51, 51, 51);background-color:rgb(243, 244, 244);">--initialize-at-build-time=<class></font>`<font style="color:rgb(51, 51, 51);">。</font>

<font style="color:rgb(51, 51, 51);">而从前面能够了解到，GraalVM Native Image在使用上最大的限制就是遵循Closed-World Assumption，这导致 Dubbo 应用在不做任何改变和支持的情况下，用户无法完整且正确地掌握Dubbo 编译所需的Reachability Metadata是什么。所以Dubbo 集成GraalVM Native Image关键在于如何能够自动、完整且准确地为用户生成Dubbo所需的Reachability Metadata，最终使得Dubbo应用能够成功编译成Native Executable，并且它能够正常运行成功。</font>

### <font style="color:rgb(51, 51, 51);">Dubbo 的 Reachability Metadata 类型</font>
<font style="color:rgb(51, 51, 51);">Dubbo 作为一个RPC框架，与之相关的Reachability Metadata主要有Reflection Metadata、Resource Metadata、Serialization Metadata和Dynamic Proxy Metadata四种。下面将会详细介绍这四种Metadata分别包含了Dubbo的哪些概念和运行原理。并且是如何在编译阶段就获取到Dubbo所需的所有Reachability Metadata。</font>

##### <font style="color:rgb(51, 51, 51);">Dubbo 中的 Reflection Metadata</font>
<font style="color:rgb(51, 51, 51);">Dubbo使用最多的就是反射。主要分为以下四个场景：</font>

<font style="color:rgb(51, 51, 51);">第一个场景就是通过反射获取服务接口的定义，Dubbo作为一个RPC框架，定义服务接口是最基本的需求，同时运行时通过反射获取接口的方法等操作也是非常频繁。这里的服务接口包括业务定义的Dubbo服务接口，同样也包括Dubbo框架内的一些内建设服务，比如比如MetricService、MetadataService等。举个简单的例子，在所有Dubbo RPC调用的入口都会经过一个代理类执行对应方法的处理器，它就是org.apache.dubbo.rpc.proxy.InvokerInvocationHandler类，它实现了java.lang.reflect.InvocationHandler接口，其invoke方法实现如下：</font>

```plain
public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
    if (method.getDeclaringClass() == Object.class) {
        return method.invoke(invoker, args);
    }
    String methodName = method.getName();
    Class<?>[] parameterTypes = method.getParameterTypes();
    if (parameterTypes.length == 0) {
        if ("toString".equals(methodName)) {
            return invoker.toString();
        } else if ("$destroy".equals(methodName)) {
            invoker.destroy();
            return null;
        } else if ("hashCode".equals(methodName)) {
            return invoker.hashCode();
        }
    } else if (parameterTypes.length == 1 && "equals".equals(methodName)) {
        return invoker.equals(args[0]);
    }

    // 省略无关代码
}
```

<font style="color:rgb(51, 51, 51);">从上述代码能够清楚地看到执行了Method的反射方法来获取方法名称等信息。所以在服务调用过程中，反射是非常常见的行为。</font>

<font style="color:rgb(51, 51, 51);">第二个场景就是SPI Extension 和 Adaptive 特性。Dubbo强大且灵活的扩展性得益于它自有的一套SPI机制，其中定义为SPI接口的实现类，以及 Adaptive 都需要用到反射，比如这些 Adaptive 类和SPI扩展的实现类，会在Dubbo启动阶段加载并且实例化，而实例化的过程中需要通过反射获取对应的构造函数来完成对象的创建。当然这里的SPI扩展实现类，也包含业务自己定义的扩展实现类，比如大家最熟悉和应用最广泛的Dubbo 执行链中的Filter，即使业务有自己的实现类，Dubbo AOT 也需要能扫描到它们并且提前加载。</font>

<font style="color:rgb(51, 51, 51);">第三个场景是多实例的启动时候需要提前加载一些相关的类，在多实例启动阶段，Dubbo会需要初始化一些单例的类，而在初始化这些类的过程中，就需要通过反射来获取这些类的构造函数。</font>

<font style="color:rgb(51, 51, 51);">第四个场景是Dubbo核心的配置类被构建成Spring Bean，比如ServiceConfig、RegistryConfig等配置类，在XML和注解的接入方式中，Dubbo的这些配置类都会被构建成Spring中的Bean对象，而在构建为Spring Bean的过程中，就会有反射的使用，这也是Spring 框架中的实现逻辑。</font>

##### <font style="color:rgb(51, 51, 51);">Dubbo 中的 Resource Metadata</font>
<font style="color:rgb(51, 51, 51);">Dubbo中也定义了一些自己的配置文件，其中主要包括两部分，一部分是SPI扩展所需的配置文件，使用Dubbo的SPI时，必须配置扩展实现的配置。才能保证SPI的实现被加载到。SPI所需的配置文件路径目录有三个：</font>

1. <font style="color:rgb(51, 51, 51);">META-INF/dubbo/internal/</font>
2. <font style="color:rgb(51, 51, 51);">META-INF/dubbo/</font>
3. <font style="color:rgb(51, 51, 51);">META-INF/services/</font>

<font style="color:rgb(51, 51, 51);">第二部分是安全的资源文件，包含了序列化所需的黑白名单。它能够防范一些序列化的RCE漏洞问题，这是Dubbo3版本新增的，为了加强服务的安全性。它的资源路径目录为</font>

security/

<font style="color:rgb(51, 51, 51);">所以 Dubbo 需要将这四个目录下的所有资源路径文件都扫描和识别出来，并提前加载到Resource Metadata配置中。</font>

##### <font style="color:rgb(51, 51, 51);">Dubbo 中的 Serialization Metadata</font>
<font style="color:rgb(51, 51, 51);">Dubbo作为一个RPC框架序列化相关的Metadata必然是有的，因为无论是作为Dubbo服务提供方并接受RPC请求,还是作为Dubbo服务的调用方进行RPC调用，都需要序列化请求和响应结果，反序列化请求和响应结果。所以与Dubbo相关的Serialization Metadata就是内外部服务方法的返回类型和请求参数类型元数据。</font>

##### <font style="color:rgb(51, 51, 51);">Dubbo 中的 Dynamic Proxy Metadata</font>
<font style="color:rgb(51, 51, 51);">Dubbo 的动态代理主要应用于在Dubbo Consumer需要生成动态代理类，用于代理远程的服务接口。屏蔽掉一些网络传输、序列化等行为的细节，让调用方能够像本地方法调用一样使用。Dubbo需要将需要代理的类都提前加载到Dynamic Proxy Metadata配置文件中，防止在运行时无法创建对应的代理类，从而影响RPC调用的正确执行流程。</font>

<font style="color:rgb(51, 51, 51);">总结一下，Dubbo需要在编译阶段获取以下五种内容，才能在编译阶段把这些内容写入到Reachability Metadata 配置文件中，保证Dubbo编译出来的Native Executable能够正确运行 ：</font>

+ <font style="color:rgb(51, 51, 51);">定义的Dubbo服务接口类、引用的Dubbo服务接口类，以及这些服务方法的返回类型和请求参数类型。</font>
+ <font style="color:rgb(51, 51, 51);">SPI Extension 和 Adaptive相关的类以及对应的SPI 配置文件</font>
+ <font style="color:rgb(51, 51, 51);">多实例启动阶段的单例类</font>
+ <font style="color:rgb(51, 51, 51);">Dubbo 核心配置类</font>
+ <font style="color:rgb(51, 51, 51);">Dubbo安全相关的资源文件</font>

<font style="color:rgb(51, 51, 51);">已经整理清楚与Dubbo相关的Reachability Metadata，那么接下来最关键的问题就是如何在编译阶段能够完整地将这些内容扫描并收集起来，然后写到对应的配置文件中。</font>

### <font style="color:rgb(51, 51, 51);">Dubbo 编译 触发的原理 </font>
<font style="color:rgb(51, 51, 51);">前面提到了在Dubbo应用中使用Native的必备plugin，也就是，dubbo-maven-plugin，Dubbo的编译触发和元数据采集就是dubbo-maven-plugin所提供的能力。在maven 的process-sources阶段，因为配置了dubbo-process-aot，所以会执行org.apache.dubbo.maven.plugin.aot.DubboProcessAotMojo，其核心逻辑在executeAot方法中，如下所示：</font>

```plain
protected void executeAot() throws Exception {
    URL[] classPath = getClassPath().toArray(new URL[0]);
    generateAotAssets(classPath, AOT_PROCESSOR_CLASS_NAME, getAotArguments(mainClass));
    compileSourceFiles(classPath, this.generatedSources, this.classesDirectory);
    copyAll(this.generatedResources.toPath(), this.classesDirectory.toPath());
    copyAll(this.generatedClasses.toPath(), this.classesDirectory.toPath());
}
```

<font style="color:rgb(51, 51, 51);">主要分为五步：</font>

1. <font style="color:rgb(51, 51, 51);">获取Class Path上的所有信息。</font>
2. <font style="color:rgb(51, 51, 51);">执行org.apache.dubbo.aot.generate.AotProcessor，采集元数据信息。</font>
3. <font style="color:rgb(51, 51, 51);">编译Java源文件和资源。</font>
4. <font style="color:rgb(51, 51, 51);">将生成的资源移动到target目录下的dubbo-aot/main/resources文件夹中。</font>
5. <font style="color:rgb(51, 51, 51);">将生成的class文件移动到target目录下的dubbo-aot/main/sources文件夹中。</font>

<font style="color:rgb(51, 51, 51);">在这五个步骤中，最关键的就是第二步，采集各类的元数据信息，接下来我们来看一下org.apache.dubbo.aot.generate.AotProcessor的细节。org.apache.dubbo.aot.generate.AotProcessor其实是一个用于main函数启动方法类，在编译阶段会执行这个main方法，入参则是应用所有的Class Path包含的资源以及生成的Reachability Metadata config.json配置文件输出的目录等信息。</font>

```plain
public static void main(String[] args) {
    String generatedSources = args[1];

    List<Class<?>> classes = ClassSourceScanner.INSTANCE.spiClassesWithAdaptive();
    NativeClassSourceWriter.INSTANCE.writeTo(classes, generatedSources);
    NativeConfigurationWriter writer = new NativeConfigurationWriter(Paths.get(args[2]), args[4], args[5]);

    // Resource Metadata
    ResourceConfigMetadataRepository resourceRepository = new ResourceConfigMetadataRepository();
    resourceRepository.registerIncludesPatterns(
            ResourceScanner.INSTANCE.distinctSpiResource().toArray(new String[] {}));
    resourceRepository.registerIncludesPatterns(
            ResourceScanner.INSTANCE.distinctSecurityResource().toArray(new String[] {}));
    for (ResourcePatternDescriber resourcePatternDescriber : getResourcePatternDescribers()) {
        resourceRepository.registerIncludesPattern(resourcePatternDescriber);
    }
    for (ResourceBundleDescriber resourceBundleDescriber : getResourceBundleDescribers()) {
        resourceRepository.registerBundles(resourceBundleDescriber);
    }
    writer.writeResourceConfig(resourceRepository);

    // Reflection Metadata
    ReflectConfigMetadataRepository reflectRepository = new ReflectConfigMetadataRepository();
    reflectRepository
            .registerSpiExtensionType(new ArrayList<>(ClassSourceScanner.INSTANCE
                    .distinctSpiExtensionClasses(ResourceScanner.INSTANCE.distinctSpiResource())
                    .values()))
            .registerAdaptiveType(new ArrayList<>(
                    ClassSourceScanner.INSTANCE.adaptiveClasses().values()))
            .registerBeanType(ClassSourceScanner.INSTANCE.scopeModelInitializer())
            .registerConfigType(ClassSourceScanner.INSTANCE.configClasses())
            .registerFieldType(getCustomClasses())
            .registerTypeDescriber(getTypes());
    writer.writeReflectionConfig(reflectRepository);
    
    // Dynamic Proxy Metadata
    ProxyConfigMetadataRepository proxyRepository = new ProxyConfigMetadataRepository();
    proxyRepository.registerProxyDescribers(getProxyDescribers());
    writer.writeProxyConfig(proxyRepository);
}
```

<font style="color:rgb(51, 51, 51);">上述的源码最关键的逻辑就是各个类型的Reachability Metadata加载和写文件，其中主要关注以下三个类：</font>

1. <font style="color:rgb(51, 51, 51);">ResourceConfigMetadataRepository：Resource Metadata的暂存类，其中换缓存了前面提到的Dubbo所需的Resource Metadata，并将这些转化为GraalVM能够识别的数据格式，写入到resource-config.json中。</font>
2. <font style="color:rgb(51, 51, 51);">ReflectConfigMetadataRepository：Reflection Metadata的暂存类，其中换缓存了前面提到的Dubbo所需的Reflection Metadata，并将这些转化为GraalVM能够识别的数据格式，写入到reflect-config.json中。</font>
3. <font style="color:rgb(51, 51, 51);">ProxyConfigMetadataRepository：Dynamic Proxy Metadata的暂存类，其中换缓存了前面提到的Dubbo所需的Dynamic Proxy Metadata，并将这些转化为GraalVM能够识别的数据格式，写入到proxy-config.json中。</font>

<font style="color:rgb(51, 51, 51);">与前面梳理的Dubbo Reachability Metadata对比，这里还缺少一种Serialization Metadata的配置信息，并且这里的Reflection Metadata并没有完全，业务自定义的Dubbo接口并没有在这个环节中被识别。这里的原因是如果Dubbo 与Spring 结合使用，并且使用的XML或者注解接入，则Dubbo能够在生成Spring Bean的过程中来生成对应接口的Serialization Metadata和Reflection Metadata。这也得益于在2022年11月，Spring6 和Spring boot 3.0的发布，在该版本中Spring 支持GraalVM Native Image作为新版本一个非常具有亮点的特性亮相。Spring AOT 能力针对Spring Bean已经有相关的解决方案，Dubbo 在XML或者注解接入方式中依赖了Spring，所以用其定义的Dubbo服务和Dubbo Consumer，都会被转化为了Spring Bean实例，这也让Dubbo能够借助Spring AOT的扩展能力，动态识别和加载用户定义的服务接口，并且写入到对应的Reachability Metadata配置文件中。Dubbo适配Spring AOT的逻辑主要在以下两个类中：</font>

1. <font style="color:rgb(51, 51, 51);">org.apache.dubbo.config.spring6.beans.factory.annotation.ReferenceAnnotationWithAotBeanPostProcessor：Dubbo Reference相关的Bean生成处理器。</font>
2. <font style="color:rgb(51, 51, 51);">org.apache.dubbo.config.spring6.beans.factory.annotation.ServiceAnnotationWithAotPostProcessor：Dubbo Service相关的Bean生成处理器。</font>

<font style="color:rgb(51, 51, 51);">它们都实现了org.springframework.beans.factory.aot.BeanRegistrationAotProcessor接口，处理Dubbo接口的AOT逻辑就集中在processAheadOfTime方法中。其主要的逻辑就是获取Bean的定义信息，将Dubbo需要传递的参数信息按照Spring Bean的AOT输出模型进行重新编织。在这里Spring 和Dubbo 都拥有自己的AOT处理逻辑，但是它们之间的处理又有些不同，Spring AOT会从源码编译开始，Spring会直接从应用的main函数启动应用程序，并且会将Spring Bean生成的Source Code生成出来，并且生成对应的Reachability Metadata。对于Spring 而言，在启动和扫描过程已经能够完成所有Metadata的扫描。下面这个就是通过Spring AOT生成的Bean Source code，当我们简单的用一个Spring 的Service 注解时。这个DemoService 的实现类就被定义为一个Spring内的Bean，而经过AOT处理后，会生成右边这个与DemoService有个的BeanDefinition的提供类，用于在加载Bean时候获取相关的信息，如下所示：</font>

```plain
/**
 * Bean definitions for {@link DemoServiceImpl}.
 */
@Generated
public class DemoServiceImpl__BeanDefinitions {
  /**
   * Get the bean definition for 'demoServiceImpl'.
   */
  public static BeanDefinition getDemoServiceImplBeanDefinition() {
    RootBeanDefinition beanDefinition = new RootBeanDefinition(DemoServiceImpl.class);
    beanDefinition.setInstanceSupplier(DemoServiceImpl::new);
    return beanDefinition;
  }
}
```

<font style="color:rgb(51, 51, 51);">Dubbo的AOT则是在源码开始编译后，会启动一个扫描的进程，也就是前面提到的org.apache.dubbo.aot.generate.AotProcessor，来完成刚刚列举的与Dubbo有关的Reachability Metadata和对应的Source code。由于我们将Spring 的Service注解换成Dubbo的DubboService注解后，同样也会得到一个与DemoService有个的BeanDefinition的提供类，如下所示：</font>

```plain
@Generated
public class ServiceBean__BeanDefinitions {
  /**
   * Get the bean instance supplier for 'ServiceBean:org.apache.dubbo.registry.DemoService::'.
   */
  private static BeanInstanceSupplier<ServiceBean> getDemoServiceInstanceSupplier() {
    return BeanInstanceSupplier.<ServiceBean>forConstructor(ModuleModel.class)
            .withGenerator((registeredBean, args) -> new ServiceBean(args.get(0, ModuleModel.class)));
  }

  /**
   * Get the bean definition for 'demoService::'.
   */
  public static BeanDefinition getDemoServiceBeanDefinition() {
    RootBeanDefinition beanDefinition = new RootBeanDefinition(ServiceBean.class);
    beanDefinition.setTargetType(ResolvableType.forClass(ServiceBean.class));
    beanDefinition.setLazyInit(false);
    beanDefinition.getPropertyValues().addPropertyValue("ref", new RuntimeBeanReference("demoServiceImpl"));
    beanDefinition.getPropertyValues().addPropertyValue("interface", "org.apache.dubbo.registry.DemoService");
    beanDefinition.getPropertyValues().addPropertyValue("parameters", Collections.emptyMap());
    beanDefinition.setInstanceSupplier(getDemoServiceInstanceSupplier());
    return beanDefinition;
  }
}
```

<font style="color:rgb(51, 51, 51);">而上面这个类的内容就是Dubbo AOT实现的。这是因为Dubbo除了将该接口定义为Spring Bean以外，还需要传递许多Dubbo独有的属性和配置，包括interface等参数。并且Dubbo也与Spring在生成的内容路径上也有所区分，Spring生成的AOT相关的内容在spring-aot目录下，而Dubbo生成的AOT相关的内容在dubbo-aot目录下。</font>

  


