### 10.1.1 可观测性概述
<font style="color:rgb(51, 51, 51);">随着微服务生态、云原生技术的发展，我们的系统与基础设施变得越发复杂，潜在的风险无处不在，传统的监控手段显的力不从心，可观测性（Observability）理论的提出与生态发展有效的提升了当下故障预防、发现、诊断问题能力。</font>

<font style="color:rgb(51, 51, 51);">对可观测性的共识比较流行的是三大支柱理论：日志（Logs）、指标（Metrics）和追踪（Tracing），Dubbo的可观测实现同样是参考三大支柱理论来进行实现，通过全方位的采集多维度指标，实现多种链路厂家的链路的适配，提供了丰富的日志错误码与专家建议，整体如下所示。</font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1739836752320-baceada1-4e0a-41ff-a37e-c464a1f0d820.png)

<font style="color:rgb(51, 51, 51);"></font>

<font style="color:rgb(0,0,0);">首先看一下可度量的系统健康状态的指标，微服务度量不仅仅是可观测性的重要组成部分，同样也是服务治理的重要组成部分。 </font>

<font style="color:rgb(0,0,0);">Dubbo通过采集指标的同时，把内部的指标数据暴露给外部的监控系统。这些观测指标中包含了很多的信息如应用、主机、Dubbo服务等。当我们发现问题的时候，可以通过观测这些指标信息，也可以通过指标的标签关联到全链路系统。之后全链路系统可以做到请求级或者应用级的性能或者异常的分析诊断。 </font>

<font style="color:rgb(0,0,0);">Dubbo 在多维度指标体系实践的时候，我们主要从两个维度来看它，如下图所示：</font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1739920605384-1e0332c7-914e-4324-9b07-00a41619a884.png)<font style="color:rgb(0,0,0);"> </font>

<font style="color:rgb(0,0,0);">第一个是纵向的维度。Dubbo 指标在采集的时候有一个采集、存储、转换、导出的流程。Dubbo为用户和开发者提供了简单易用的接入门面。接入后服务在运行过程中通过指标采集器进行指标的采集。Dubbo中提供了非常多的指标采集器，包括聚合和非聚合的指标采集等等。 </font>

<font style="color:rgb(0,0,0);">采集的指标会通过内存变量值临时存储在内存里，之后会有部分指标（如QPS 等带有滑动窗口的最小值、最大值的聚合指标）进行聚合计算，最后这些指标可以导出到外部系统进行使用。</font>

<font style="color:rgb(0,0,0);">Dubbo支持在QPS 服务质量服务中进行指标导出，通过HTTP请求也可以直接访问进行指标的查询，查询到的指标也可以导出到 Prometheus中进行存储 。 </font>

<font style="color:rgb(0,0,0);">第二个是横向的维度。Dubbo 指标采集覆盖了非常容易出现异常的地方。比如 Dubbo 提供了三大中心，包括注册中心、元数据中心、配置中心，另外一个比较关键的是 RPC 链路上的数据采集，比如请求相关的耗时、异常指标等等，还有一些关于 Dubbo 线程池资源信息的指标采集。 </font>

<font style="color:rgb(0,0,0);">关于链路追踪的实现，Dubbo通过提供全链路门面，只需进行非常简易的操作，依赖引入和配置就可以直接把数据导出到各大全链路平台。无论企业使用哪个流行的全链路平台，在后期升级 Dubbo 后都可以直接把链路数据导出去。</font>

<font style="color:rgb(0,0,0);">另外，链路系统还包含全链路的 TraceId 或者局部的 SpanId。通过全链路的 ID，我们可以在链路系统中直接跳转到日志平台。在日志平台里包含非常详细的日志上下文，这些日志 上下文可以提供非常精确的异常问题诊断。 </font>

<font style="color:rgb(0,0,0);">关于日志的设计Dubbo也提供了非常详细的错误码机制和专家建议的形式，通过日志错误码可以直接导航到官网上的帮助文档。</font>

### <font style="color:rgb(0,0,0);">10.1.2 指标</font>
**<font style="color:rgb(0,0,0);">指标采集方法论</font>**

<font style="color:rgb(0,0,0);">具体 Dubbo 的采集需要哪些指标呢？通过目前流行的一些方法论如下：  </font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1739920639112-844481f4-301a-4588-a7ae-c288da8a43fb.png)

+ <font style="color:rgb(0,0,0);">图中第一张图是谷歌 SRE 书中提到的四大黄金指标。它是根据谷歌大规模的分布式服务监控总结出来的，可以进行请求级别服务质量的衡量，主要包含延迟、流量、错误以及饱和度。</font>
+ <font style="color:rgb(0,0,0);">图中第二张图是 RED 方法。它更侧重于请求，从外部视角来查看服务的健康状态，主要包含速率、错误与持续时间。 </font>
+ <font style="color:rgb(0,0,0);">图中第三张图是 USE 方法。它更侧重于系统内部的资源使用情况，包含利用率、饱和度与错误。 </font>

<font style="color:rgb(0,0,0);">可以看到，以上三个指标的方法论中都包含错误和性能的指标，而错误和性能也是每个开发者需要重点关注的。</font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1739920769191-49fdc654-ac63-4b7e-a7dd-fcfa6719f381.png)

**<font style="color:rgb(0,0,0);">使用指标采集</font>**

<font style="color:rgb(0,0,0);">然后我们进行了指标的系统完善。在 Dubbo 3.2 版本中，多维度指标体系已经完成，而且也在快速持续的版本迭代中。在这个版本中我们只需要引入一个快速集成的自定义Spring Boot Starter 包就可以实现指标的自动采集。之后我们通过 Dubbo 的 QPS 服务质量端口即可直接访问到。可以通过浏览器或者curl 命令访问 22222 端口，后面加一个 metrics 路径，这样就可以看到非常详细的默认指标的导出。 </font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1739920788864-ff335967-8d5b-4f47-9416-66df9a15521e.png)

<font style="color:rgb(0,0,0);">当使用普罗米修斯导出指标时候，指标名字是这些带有 Dubbo 前缀、类型（不同模块，比如消 费者提供的请求级别，三大注册中心、线程池等）、行为、单位，命名则参考的是 Prometheus 的官方格式。 </font>

<font style="color:rgb(0,0,0);">多维度指标体系有些人可能会直接复用 Spring Boot 默认的 manager 管理端口，Dubbo 也适配了一下 Spring Boot Actuator 的扩展。 </font>

<font style="color:rgb(0,0,0);">操作和刚刚一样，只是引入 Spring Boot Starter 包。后面也无需做任何其他的配置，就可以在 Spring 端口里看到详细的指标了。包括 Spring Boot 内置的 JVM 指标、Dubbo 指标等等。</font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1739920942331-9bd136e1-4536-4531-8894-06529b0c6c32.png)

<font style="color:rgb(0,0,0);">指标体系接入之后，我们如果直接通过命令行访问只能看到一些瞬时的数据，但在监控指标体系中我们其实更关注的是多维度的向量数据。如果我们把这些数据看作是一个点其实是比较难看出问题的，所以我们需要把这些数据存储起来来观测。</font>

<font style="color:rgb(0,0,0);"> Dubbo 默认提供对 Prometheus 采集的接入。Prometheus 作为指标存储与监控一体的监控系统，提供了很多的服务发现模型。比如我们直接把服务部署在 K8S 上，可以直接基于 K8S 的服务发现机制进行指标采集，只要能发现 Dubbo 服务的 IP 和服务接口，就可以进行指标采集。采集到的指标会自动存储在 Prometheus 的时序数据库里。</font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1739920982037-d0786905-843d-48dc-8aa0-d7639df74206.png)<font style="color:rgb(0,0,0);">上图是我们通过 Prometheus 的查询框查询出来的响应时间的最新指标。Prometheus 的指标更侧重于存储与报警，如果我们想更直观的体现还需要接入 Grafana。 </font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1739921011408-5a56ab2d-b122-4ea7-ac6b-4a4f00d10d85.png)

<font style="color:rgb(0,0,0);">Grafana 的目标是为企业提供简易接入的监控面板，上图是一个简易的全局大盘。 </font>

<font style="color:rgb(0,0,0);">我们通过筛选框可以做应用/机器IP/服务接口等维度的筛选查询，查询服务的健康状态。可以看到，这些指标基本上都是基于前面总结的方法论实现的。比如 QPS、请求数量、成功率、失败率、请求的时延等等。 </font>

<font style="color:rgb(0,0,0);">此外，还有一些应用信息的指标，比如升级版本时，想看到哪些应用已经升级到新的版本，就可以通过标签信息看到新的应用的版本号，当然也会有应用信息的实例 IP 分布，还有一些线程资源等等。 </font>

<font style="color:rgb(0,0,0);">刚才说的指标更利于帮助我们发现问题，更多的场景下需要进行系统之间的问题的诊断。微服务系统往往是多个系统之间有关联关系，所以服务之间的诊断更依赖于全链路系统。 </font>

### <font style="color:rgb(0,0,0);">10.1.3 全链路</font>
<font style="color:rgb(0,0,0);">Dubbo 通过适配各大厂商的全链路追踪系统，快速适配接入的用户，只需增加少量的配置就可以实现链路数据的导出。</font>

<font style="color:rgb(0,0,0);">在链路追踪门面的客户端实现中， OpenTelemetry，大家应该非常熟悉，它支持多语言，规范标准统一的 API，支持大部分流行的第三方厂商的全链路追踪系统，是 CNCF 孵化的项目之一，很多中间件应用都已经接入了这种规范。 Micrometer，大家可能对它的印象是指标采集的接入。它的缺点是只能支持 Java，但它在语言方面，优点是 Spring Boot 3 默认的指标采集，链路采集默认支持 Micrometer Tracing 的功能。此外，Micrometer 它还可以通过桥接包直接转化为 OTEL 的协议，间接也支持各种第三方的采集。并且 Micrometer 自身也通过桥接机制可以桥接很多的全链路厂商。当前实现中链路采集客户端使用到是与指标采集统一的客户端Micrometer，使用 Micrometer 后无需额外引入第三方的依赖，只需使用 Micrometer Tracing 的桥接包，就可以快速的接入。 </font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1739941582991-cb4d933d-090c-40ba-84f9-cec47a64f701.png)

<font style="color:rgb(0,0,0);">上图是链路追踪系统的简单结构。Dubbo 的链路采集主要采集 RPC 请求的链路。在消费者发起请求的时候，如果存在链路 ID 就直接复用，没有的话会产生链路 ID，然后把它们上采集器。同样消费者也会通过 RPC 的上下文把链路数据透传给提供端。提供端拿到这个链路数据后，会对它进行父子关系的关联。最后把这些链路数据上报采集器。 </font>

<font style="color:rgb(0,0,0);">采集器在前面主要是内存级别的操作，对系统的损耗比较小。后面将进行异步的导出，和前面指标体系是一样的。内存级的同步采集，异步的把数据导出到第三方的链路系统。 </font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1739941565275-5b11ad5e-9759-426b-a8bf-7e83bd824a4a.png)

<font style="color:rgb(0,0,0);">链路系统接入也比较简单，主要是引入Spring Boot Starter 的依赖包，进行一些比较简单的配置，包括不同厂商的导出地址配置等等。 </font>

<font style="color:rgb(0,0,0);">链路系统可以帮助大家分析性能与异常，但一些系统问题原因的排查可能需要更详细的日 志上下文来进行关联。这个时候这个链路系统会把数据放到日志系统的上下文 MDC （</font><font style="color:rgb(77, 77, 77);">Mapped Diagnostic Context，映射调试上下文，一般使用</font><font style="color:#080808;background-color:#ffffff;">ThreadLocal存储</font><font style="color:rgb(0,0,0);">）里面， 然后在日志在上下文里把链路系统存入的内容取出来，展示到日志的文件里。 </font>

<font style="color:rgb(0,0,0);">日志文件可能也会接触到第三方的日志平台，如果有二次开发能力，可以在这种系统平台里加上链接，让这些 TraceId 自动跳转，即全链路系统自动跳转到日志平台，日志平台也可以自动跳转到全链路系统，查询问题非常高效。</font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1739941542296-129762b9-96bb-46ee-a2db-483c8c17944b.png)

<font style="color:rgb(0,0,0);">上图是接入 Zipkin 的展示页面。可以看到它可以进行应用级的性能分析和接口级的性能分 析。还可以看到一些 Dubbo 元数据，它的标签可以和指标大盘指标体系进行关联关系。 </font>

<font style="color:rgb(0,0,0);">这是 Skywalking 的格式，包括列表形式、表格形式等等。它通过 Traceid 搜到全链路的 请求链路，也可以进行性能和异常的诊断。</font>

### <font style="color:rgb(0,0,0);">10.1.4 日志</font>
<font style="color:rgb(0,0,0);">Dubbo 通过日志门面的形式适配了各大日志组件。因为我们的日志组件在后期发展的体系 是非常多的，可能是历史原因。不过 Dubbo 已经通过门面的形式适配了各大日志组件。 </font>

<font style="color:rgb(0,0,0);">基于此，Dubbo 做了一个专家建议帮助的文档手册。升级到 Dubbo 3 版本后，可以看到 出现异常时日志里会有一个帮助文档的 FAQ 链接的形式。这个帮助手册套里提供了一些问 题可能出现的原因和排查问题的解决思路。 </font>



 

 

