## 10.2.1 观测指标
前面章节概述了Dubbo指标的理论信息，这里我们开始来看下Dubbo中的指标是如何实现采集的，从代码结构上看主要包含了如下模块：

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1740012075724-10710f95-750c-4bea-b406-1f435e70b4d2.png)

其中dubbo-metrics-api是指标模块为Dubbo提供的与第三方无关的基础指标规范与能力，其中包含指标的采集、存储、转换、导出等，dubbo-metrics-default则是当前指标规范下的实现，目前默认的实现是基于Micrometer依赖，另外几个模块则如下所示：

| **模块** | **说明** |
| --- | --- |
| dubbo-metrics-api | 度量指标的核心接口和工具类 |
| dubbo-metrics-default | 默认的度量指标收集和管理功能的具体实现 |
| dubbo-metrics-event | 处理度量事件的模块，用于解耦 |
|  dubbo-metrics-config-center | 与配置中心集成，提供度量指标的配置管理 |
| dubbo-metrics-metadata | 元数据中心相关度量功能 |
| dubbo-metrics-registry | 注册中心相关度量功能 |
| dubbo-metrics-netty | 网络IO模块Netty相关度量功能 |
| dubbo-metrics-prometheus | 指标导出Prometheus数据格式功能 |


根据源码模块可以简单的了解到Dubbo通过分层结构采集了不同维度的指标，那指标是如何进行采集，对外又是如何提供数据呢，接下来就来通过通过一些指标采集的流转来看整个过程。

对于SpringBoot的指标使用，首先需要额外引入如下依赖：

```xml
<dependency>
  <groupId>org.apache.dubbo</groupId>
  <artifactId>dubbo-observability-spring-boot-starter</artifactId>
  <version>${dubbo.version}</version>
</dependency>
```

在SpingBoot的yaml配置中可以通过如下方式来开启指标：

```yaml
# management中有SpringBoot导出指标的配置
management:
  metrics:
    tags:
      application: dubbo-samples-metrics-spring-boot
    export:
      prometheus:
        enabled: true
  server:
    port: 8081
  endpoints:
    web:
      base-path: /management
      exposure:
        include: info,health,env,prometheus
  endpoint:
    metrics:
      enabled: true
    prometheus:
      enabled: true
#下面是dubbo将指标导出到普罗米修斯配置
dubbo:
  application:
    name: metrics-provider
  registry:
    address: zookeeper://${ZOOKEEPER_ADDRESS:127.0.0.1}:2181
  metrics:
    protocol: prometheus
  qos:
    enable: true
    port: 22222
    accept-foreign-ip: false
```

关于指标采集的流转接下来可以通过一个简单的应用信息指标的采集流转来详细看下，在应用启动生命周期的默认应用发布器<font style="color:#080808;background-color:#ffffff;">DefaultApplicationDeployer</font>的初始化的时候，进行了一些可观测的初始化逻辑，如下所示：

<font style="color:#080808;background-color:#ffffff;">DefaultApplicationDeployer的initialize方法：</font>

```java
@Override
public void initialize() {
    //初始化导出器
    initMetricsReporter();
    //初始化内置MetricsService服务
    initMetricsService();
    // @since 3.2.3
    //可观测信息的初始化 主要用于trace
    initObservationRegistry();

}
```

其中我们重点来看initMetricsReporter方法

```java
private void initMetricsReporter() {
        ...
        //获取默认的指标采集器，
        DefaultMetricsCollector collector = applicationModel.getBeanFactory().getBean(DefaultMetricsCollector.class);
        ...
        //默认开启指标采集
        collector.setCollectEnabled(true);
        //采集应用数据
        collector.collectApplication();
        ... 
    }
```

DefaultMetricsCollector默认指标采集器对象是在MetricsScopeModelInitializer的<font style="color:#080808;background-color:#ffffff;">initializeApplicationModel</font>

 方法中初始化<font style="color:#080808;background-color:#ffffff;">MetricsDispatcher的构造器中进行了初始化，关于initializeApplicationModel方法具体调用环节可以看生命周期章节，这里通过获取applicationModel的beanFactory对象，意味着我们的采集器在Dubbo三层数据模型架构中处于应用级，一个应用级则会创建一个默认采集器，</font>

接下来我们通过采集器<font style="color:#080808;background-color:#ffffff;">DefaultMetricsCollector来看下应用信息是如何进行采集、存储、导出的，接下来就来看下默认采集器的collectApplication方法，如下所示：</font>

```java
public void collectApplication() {
    this.setApplicationName(applicationModel.getApplicationName());
    applicationSampler.inc(applicationName, MetricsEvent.Type.APPLICATION_INFO);
}
```

在Dubbo的默认指标采集器中，通过创建应用指标计数器样本对象<font style="color:#080808;background-color:#ffffff;">SimpleMetricsCountSampler来进行指标数据计算与存储，接下来可以看下applicationSampler.inc的方法是如何统计应用信息的。</font>

<font style="color:#080808;background-color:#ffffff;">SimpleMetricsCountSampler的inc方法处理应用信息指标</font>

```java
@Override
public void inc(S source, K metricName) {
    getAtomicCounter(source, metricName).incrementAndGet();
}
```

<font style="color:#080808;background-color:#ffffff;">在inc方法中先获取用于存储指标的计数器对象，然后再进行计数，一个可观测的指标不仅仅要包含指标数据，也需要包含多维度的指标信息，那接下来可以看下通过getAtomicCounter方法来初始化指标信息和指标数据对象如下所示：</font>

<font style="color:#080808;background-color:#ffffff;">SimpleMetricsCountSampler的getAtomicCounter方法</font>

```java
//成员变量metricCounter 用于缓存指标信息
//K为指标类型，M为指标Key，值为指标计数器
private final Map<K, ConcurrentMap<M, AtomicLong>> metricCounter = new ConcurrentHashMap<>();
...
private AtomicLong getAtomicCounter(S source, K metricsName) {
    //计数器指标样本配置，用于存储指标信息
    MetricsCountSampleConfigurer<S, K, M> sampleConfigure = new MetricsCountSampleConfigurer<>();
    //指标来源系统信息（这里是系统名）
    sampleConfigure.setSource(source);
    //指标名 MetricsEvent.Type.APPLICATION_INFO 枚举类型
    sampleConfigure.setMetricsName(metricsName);
    //模版方法 metric 生成指标对象（生成对应类型的指标）
    //这里在默认采集器中的实现生成的应用指标对象为ApplicationMetric
    this.countConfigure(sampleConfigure);
    //metricCounter为指标名到指标映射关系的对象
    Map<M, AtomicLong> metricAtomic = metricCounter.computeIfAbsent(metricsName, k -> new ConcurrentHashMap<>());
    ...
    //获取指标计数器
    //metricAtomic为指标信息到指标计数器映射关系的对象
    AtomicLong atomicCounter = metricAtomic.computeIfAbsent(sampleConfigure.getMetric(), k -> new AtomicLong());

    return atomicCounter;
}
```

在getAtomicCounter方法中主要用来创建并初始化指标配置，同时缓存指标配置到成员变量中，在这里应用信息的指标类型为MetricsEvent.Type.APPLICATION_INFO，指标信息对象为<font style="color:#080808;background-color:#ffffff;">ApplicationMetric，指标计数器类型为AtomicLong，获取完指标计数器对象之后在inc方法中将会为指标值增加计数，指标数据生成之后将会缓存在SimpleMetricsCountSampler类型的成员变量metricCounter中，应用信息的指标在启动初始化应用发布器的时候就完成了采集并且为单纯的内存计算，最终将结果存储在metricCounter容器中，那存储的指标如何能被外部观测系统感知到呢，接下来就来看下指标数据导出逻辑</font>。

Dubbo默认的实现中兼容了多种导出指标的场景其中包含QOS端点、QOS命令行、SpringBoot的<font style="color:#080808;background-color:#ffffff;">endpoint，也支持业务系统获取Dubbo指标自行导出，接下来我们就以SpringBoot的endpoint导出指标的形式来看下Dubbo的指标是如何与SpringBoot融合导出。</font>

<font style="color:#080808;background-color:#ffffff;">在SpringBoot2.0版本开始实现了metrics模块，默认支持使用Micrometer依赖库来采集指标，Micrometer提供了可观测门面来支持Java系统的指标采集，链路追踪等场景，Micrometer内部提供了</font><font style="color:rgba(0, 0, 0, 0.9);">计时器</font>**<font style="color:rgba(0, 0, 0, 0.9);">、</font>**<font style="color:rgba(0, 0, 0, 0.9);">仪表</font>**<font style="color:rgba(0, 0, 0, 0.9);">、</font>**<font style="color:rgba(0, 0, 0, 0.9);">计数器</font>**<font style="color:rgba(0, 0, 0, 0.9);">、</font>**<font style="color:rgba(0, 0, 0, 0.9);">分布摘要和长任务计时器等指标类型，在Micrometer中的指标可以统称为</font><font style="color:#080808;background-color:#ffffff;">Meter，指标对象可以用来处理指标元数据和指标计数器，</font><font style="color:rgba(0, 0, 0, 0.9);">Micrometer中还存在一个重要的概念称为注册表</font><font style="color:rgb(25, 30, 30);">MeterRegistry，指标注册表作为指标容器统一对指标进行存储、导出，指标注册表的实现有</font><font style="color:rgb(36, 41, 46);background-color:rgb(246, 248, 250);">CompositeMeterRegistry（用于管理多个注册表但不进行指标数据存储），</font><font style="color:rgb(25, 30, 30);">SimpleMeterRegistry（内存级指标管理）、</font><font style="color:#080808;background-color:#ffffff;">PrometheusMeterRegistry（支持普罗米修斯格式的注册表）,下面可以通过一个例子来简单看下Micromerter的用法，首先引入Micrometer依赖：</font>

```xml
<dependency>
    <groupId>io.micrometer</groupId>
    <artifactId>micrometer-registry-prometheus</artifactId>
    <version>1.10.2</version>
    <exclusions>
        <exclusion>
            <artifactId>micrometer-core</artifactId>
            <groupId>io.micrometer</groupId>
        </exclusion>
    </exclusions>
</dependency>
<dependency>
    <groupId>io.micrometer</groupId>
    <artifactId>micrometer-core</artifactId>
    <version>1.10.2</version>
</dependency>
```

接下来实现一个MicrometerTest类型，通过创建计数器，然后将计数器通过Http接口的形式提供给外部，如下代码所示：

```java
public class MicrometerTest
{
    public static void main( String[] args )
    {
        // 组合注册表
        CompositeMeterRegistry composite = new CompositeMeterRegistry();
        // 内存注册表
        MeterRegistry registry = new SimpleMeterRegistry();
        composite.add(registry);
        // 普罗米修斯注册表
        PrometheusMeterRegistry prometheusRegistry = new PrometheusMeterRegistry(PrometheusConfig.DEFAULT);
        composite.add(prometheusRegistry);
        // 计数器
        Counter compositeCounter = composite.counter("counter");
        // 计数
        compositeCounter.increment();
        try {
            // 暴漏8080端口来对外提供指标数据
            HttpServer server = HttpServer.create(new InetSocketAddress(8080), 0);
            server.createContext("/prometheus", httpExchange -> {
                // 获取普罗米修斯指标数据文本内容
                String response = prometheusRegistry.scrape();
                // 指标数据发送给客户端
                httpExchange.sendResponseHeaders(200, response.getBytes().length);
                try (OutputStream os = httpExchange.getResponseBody()) {
                    os.write(response.getBytes());
                }
            });

            new Thread(server::start).start();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
```

<font style="color:rgba(0, 0, 0, 0.9);">过运行main方法，在浏览器中访问地址：http://localhost:8080/prometheus 就可以看到我们程序提供给普罗米修斯监控的指标了，如下图所示：</font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1740491693031-a2733d28-673d-454d-a8e3-c6544f52df76.png)

<font style="color:#080808;background-color:#ffffff;">了解完Micrometer指标采集后，接下来可以看下Dubbo指标是如何导出的，首先我们通过如下命令访问Dubbo的QOS端口的metrics 路径，经过筛选可以获取如下指标信息：</font>

```bash
mac@MacdeMacBook-Pro github % curl http://localhost:22222/metrics |grep dubbo_appl# HELP dubbo_application_info_total Total Application Info
# TYPE dubbo_application_info_total counter
dubbo_application_info_total{application_module_id="1.1",application_name="metrics-provider",application_version="3.3.3",git_commit_id="c3b51be2080d8693bce1ac8a244ee8f9c5feaae9",hostname="MacdeMacBook-Prohah.local",ip="************",} 1.0
```

<font style="color:#080808;background-color:#ffffff;">上面返回的数据格式是普罗米修斯监控系统支持的数据格式，其中 </font>`<font style="color:#080808;background-color:#ffffff;"># HELP</font>`<font style="color:#080808;background-color:#ffffff;">  提供了指标的描述 </font>`<font style="color:#080808;background-color:#ffffff;"># TYPE</font>`<font style="color:#080808;background-color:#ffffff;"> 描述指标的类型，</font>`<font style="color:#080808;background-color:#ffffff;">dubbo_application_info_total</font>`<font style="color:#080808;background-color:#ffffff;"> 是指标的名称。花括号 {} 中包含了一些键值对，称为标签（labels），它们为指标提供了额外的上下文信息：</font>

+ <font style="color:#080808;background-color:#ffffff;">application_module_id="1.1"：应用程序模块的 ID。</font>
+ <font style="color:#080808;background-color:#ffffff;">application_name="metrics-provider"：应用程序的名称。</font>
+ <font style="color:#080808;background-color:#ffffff;">application_version="3.3.3"：应用程序引入的Dubbo版本。</font>
+ <font style="color:#080808;background-color:#ffffff;">git_commit_id="c3b51be2080d8693bce1ac8a244ee8f9c5feaae9"：与该版本应用程序关联的 Git 提交 ID。</font>
+ <font style="color:#080808;background-color:#ffffff;">hostname="MacdeMacBook-Prohah.local"：运行应用程序的机器的主机名。</font>
+ <font style="color:#080808;background-color:#ffffff;">ip="************"：运行应用程序的机器的 IP 地址。</font>

<font style="color:#080808;background-color:#ffffff;">最后的1.0 是计数器的值，表示总的应用程序信息计数为 1.0。</font>

<font style="color:#080808;background-color:#ffffff;">接下来就通过请求来看源码，在指标模块dubbo-metrics-prometheus中扩展了Dubbo的QOS服务其中自定义的指标实现在PrometheusMetricsReporterCmd中，当请求QOS接口的metrics路径时请求将抵达execute方法，如下代码所示：</font>

```java
@Cmd(name = "metrics", summary = "reuse qos report")
public class PrometheusMetricsReporterCmd implements BaseCommand {
    ...
    @Override
    public String execute(CommandContext commandContext, String[] args) {
        //获取当前框架模型中所有的应用模型
        List<ApplicationModel> models = frameworkModel.getApplicationModels();
        ...
        result = useFirst(models, result);
        ...
        return result;
    }
    private String useFirst(List<ApplicationModel> models, String result) {
        ...
        String current = getResponseByApplication(model);
        ...
        result = current;
        ...
        return result;
    }
    private String getResponseByApplication(ApplicationModel applicationModel) {
        //获取指标导出器
        MetricsReporter metricsReporter = applicationModel.getBeanFactory().getBean(PrometheusMetricsReporter.class);
        ...
        //如果指标发生了变化则刷新对应指标
        metricsReporter.resetIfSamplesChanged();
        ...
        //获取指标内容
        response = metricsReporter.getResponse();

        return response;
    }
}
```

<font style="color:#080808;background-color:#ffffff;">在指标导出的过程中有一个需要我门重点关注的地方是getResponseByApplication方法中resetIfSamplesChanged方法调用，这个方法存在的原因是因为Dubbo自身的指标存储是与第三方厂商无关的，当有指标数据发生变更时候则执行这个方法将Dubbo的指标转换为对应导出器所属厂商的指标即可，这里执行的是 AbstractMetricsReporter的resetIfSamplesChanged方法，代码如下所示：</font>

```java
public void resetIfSamplesChanged() {
    collectors.forEach(collector -> {
        //指标未变化直接使用缓存中的数据
        if (!collector.calSamplesChanged()) {
            return;
        }
        //获取所有Dubbo指标样本
        List<MetricSample> samples = collector.collect();
        for (MetricSample sample : samples) {
           ...
           //将Dubbo指标样本转换为Micrometer指标，注册在Micrometer注册表中
           registerSample(sample);
           ...
    });
}
```

<font style="color:#080808;background-color:#ffffff;">在resetIfSamplesChanged方法中先通过遍历所有指标采集器，然后依次判断指标采集器是否发生了变更，如果指标采集器发生了变更则获取当前指标采集器下的所有Dubbo指标样本，然后将其注册到目标注册表中，接下来我们来看下当前Dubbo指标转换、注册到Micrometer注册表的代码，如下AbstractMetricsReporter类型的registerSample方法：</font>

```java
private void registerSample(MetricSample sample) {
    switch (sample.getType()) {
        case GAUGE:
            registerGaugeSample((GaugeMetricSample) sample);
            break;
        case COUNTER:
            registerCounterSample((CounterMetricSample) sample);
    ...
    }
}
```

<font style="color:#080808;background-color:#ffffff;">目前Dubbo指标类型中主要支持GAUGE（仪表可增可减）、COUNTER（计数器只增不减）两种类型，我们参考registerCounterSample方法来看：</font>

```java
private void registerCounterSample(CounterMetricSample sample) {
    //指标名与指标值
    FunctionCounter.builder(sample.getName(), sample.getValue(), Number::doubleValue)
    //指标描述信息
    .description(sample.getDescription())
    //指标标签信息
    .tags(getTags(sample))
    //指标注册表
    .register(compositeRegistry);
}
```

可以看到在Dubbo指标向Micrometer指标注册时候仅仅是将指标注册到了一个组合注册表compositeRegistry中，那Micrometer指标又是如何转换为普罗米修斯支持的格式呢，其实在前面Micrometer的例子中已经详细的进行了说明组合指标注册器用于管理其它指标注册表，PrometheusMeterRegistry指标注册器则用于处理普罗米修斯数据，在<font style="color:#080808;background-color:#ffffff;">PrometheusMetricsReporter导出器中已经</font>将PrometheusMeterRegistry注册表添加到了组合注册表<font style="color:#080808;background-color:#ffffff;">CompositeMeterRegistry</font>中，这里注册在组合注册表中的指标数据后续在PrometheusMeterRegistry中也可以获取到，最后再回过头来看下指标转换后的获取指标内容方法，<font style="color:#080808;background-color:#ffffff;">PrometheusMetricsReporter的getResponse方法，如下所示：</font>

```java
public String getResponse() {
    return prometheusRegistry.scrape();
}
```

<font style="color:#080808;background-color:#ffffff;"> 可以看到getResponse方法中仅仅调用了一个scrape方法来获取所有指标数据，这个方法的作用是获取 Prometheus 注册表中的所有指标数据，并将其作为字符串返回，到这里指标采集、导出已经完成了，指标数据可有效的反映系统的整体健康状况，但是无法提供每个请求的详细信息，往往通过指标快速发现异常，然后通过指标关联的服务、链路信息来分析系统中服务调用的异常。</font>

## <font style="color:#080808;background-color:#ffffff;">10.2.2 链路追踪</font>
<font style="color:#080808;background-color:#ffffff;">指标（Metrics）通常提供系统的整体健康状况和性能数据，例如请求数、错误率、响应时间等。但这些数据是聚合的，无法提供单个请求的详细信息。而链路追踪（Tracing）可以记录每个请求在系统中的完整路径，包括每个服务调用的详细信息，更有助于故障排查和性能分析。Dubbo的链路追踪通过</font><font style="color:rgb(33, 37, 41);"> Micrometer Observation 完成 Tracing 的所有埋点工作，依赖 Micrometer 支持多种厂商的 Bridge 适配，我们可以实现将 Tracing 导入各种后端系统，具体工作原理如下：</font>![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1741012216712-e0918e49-9847-436a-a84f-246c810b68d1.png)

<font style="color:#080808;background-color:#ffffff;">在详细了解下Dubbo中是如何来实现链路追踪，先来认识几个Micrometer的一些术语，如下所示：</font>

| **<font style="background-color:rgb(252, 252, 252);">术语</font>** | **<font style="background-color:rgb(252, 252, 252);">说明</font>** |
| :---: | --- |
| **<font style="background-color:rgb(252, 252, 252);">Trace</font>** | <font style="background-color:rgb(252, 252, 252);">由Span构成的树状结构，代表分布式系统中的完整请求轨迹（如一次订单支付涉及的多个服务调用）</font> |
| **<font style="background-color:rgb(252, 252, 252);">Span</font>** | <font style="background-color:rgb(252, 252, 252);">基本工作单元，记录服务内部逻辑的执行时段及上下文（如数据库查询、RPC调用）</font> |
| **<font style="background-color:rgb(252, 252, 252);">Baggage</font>** | <font style="background-color:rgb(252, 252, 252);">跨服务传递的键值对数据（如用户ID、交易流水号），全局透传但需注意数据量控制</font> |
| **<font style="background-color:rgb(252, 252, 252);">Annotation</font>** | <font style="background-color:rgb(252, 252, 252);">记录Span生命周期中的关键事件（如异常抛出、缓存命中）</font> |
| **<font style="background-color:rgb(252, 252, 252);">Tracer</font>** | <font style="background-color:rgb(252, 252, 252);">管理Span生命周期的核心组件，支持创建/启动/停止Span，并通过Reporter上报数据</font> |
| **<font style="background-color:rgb(252, 252, 252);">Exporter</font>** | <font style="color:rgb(33, 37, 41);">处理和导出遥测数据</font> |
| **<font style="color:#080808;background-color:#ffffff;">Brave</font>** | <font style="color:rgb(31, 35, 40);">Zipkin 后端服务兼容的 Java 分布式追踪实现</font> |
| **<font style="color:rgb(0, 0, 0);">OpenTelemetry</font>** | <font style="color:rgb(0, 0, 0);"> API、SDK 和工具的集合。用来检测、生成、收集和导出遥测数据（指标、日志和跟踪）</font> |
| **<font style="color:rgb(11, 12, 20);">Zipkin</font>** | <font style="color:rgb(11, 12, 20);background-color:rgb(247, 248, 252);"> </font><font style="color:rgb(11, 12, 20);">一个分布式跟踪系统，最初由 Twitter 开发，用于收集排除复杂微服务架构故障所需的及时数据</font> |


对于 SpringBoot 用户，<font style="color:#080808;background-color:#ffffff;">在Dubbo中内置了一些链路追踪的实现方案，</font>Dubbo 内部则提供了 Tracing 相关的 starters，用于自动装配 Micrometer 相关的配置代码，且用户可自由选择 Tracer和Exporter，当前版本中主要提供如下几种依赖集成方式：

<font style="color:#080808;background-color:#ffffff;">1）</font><font style="color:rgb(0, 0, 0);">OpenTelemetry</font><font style="color:#080808;background-color:#ffffff;"> 作为 Tracer，将 Trace 信息 export 到 Zipkin</font>

```xml
<dependency>
  <groupId>org.apache.dubbo</groupId>
  <artifactId>dubbo-tracing-otel-zipkin-spring-boot-starter</artifactId>
  <version>${version}</version>
</dependency>
```

<font style="color:#080808;background-color:#ffffff;">2）Brave 作为 Tracer，将 Trace 信息 export 到 Zipkin</font>

```xml
<dependency>
  <groupId>org.apache.dubbo</groupId>
  <artifactId>dubbo-tracing-brave-zipkin-spring-boot-starter</artifactId>
  <version>${version}</version>
</dependency>
```

<font style="color:#080808;background-color:#ffffff;">3）</font><font style="color:rgb(0, 0, 0);">OpenTelemetry</font><font style="color:#080808;background-color:#ffffff;"> 作为 Tracer，将 Trace 信息 export 到 OTLP Collector</font>

```xml
<dependency>
  <groupId>org.apache.dubbo</groupId>
  <artifactId>dubbo-tracing-otel-otlp-spring-boot-starter</artifactId>
  <version>${version}</version>
</dependency>

```

无论采用哪种链路采集与导出方案，整体的采集与导出过程比较相似，接下来则以SpringBoot项目中引入`dubbo-spring-boot-tracing-brave-zipkin-starter`为例，使用<font style="color:#080808;background-color:#ffffff;">Brave 作为 Tracer，将 Trace 信息 export 到 Zipkin，</font>首先引入依赖如下所示：

```xml
<!-- Brave as Tracer, Zipkin as exporter -->
<dependency>
    <groupId>org.apache.dubbo</groupId>
    <artifactId>dubbo-spring-boot-tracing-brave-zipkin-starter</artifactId>
</dependency>
```

<font style="color:#080808;background-color:#ffffff;">然后是链路追踪的配置，我们可以在application.yaml中增加如下Dubbo配置：</font>

```yaml
dubbo:
  tracing:
    enabled: true # 开启状态默认为false
    sampling:
      probability: 0.5 # 采样率, 默认是 0.1
    propagation:
      type: W3C # 传播器类型：W3C/B3 默认是W3C
    tracing-exporter:
      zipkin-config:
        endpoint: http://localhost:9411/api/v2/spans
        connect-timeout: 1s # 建立连接超时时间, 默认为1s
        read-timeout: 10s # 传递数据超时时间, 默认为10s

# tracing信息输出到logging
logging:
  pattern:
    level: '%5p [${spring.application.name:},%X{traceId},%X{spanId}]'
```

<font style="color:#080808;background-color:#ffffff;">上面的配置是针对在当前机器上部署的Zipkin来处理的，关于Zipkin的部署相对比较简单，可以直接参考官网一键启动的方式。</font>

<font style="color:#080808;background-color:#ffffff;">通过这种自动装配的方式来配置链路追踪可以快速的进行链路功能的接入。为了方便理解原理接下来我们可以通过实现过程来查看使用</font><font style="color:rgb(33, 37, 41);">Micrometer Observation时进行哪些配置，首先先从</font><font style="color:#080808;background-color:#ffffff;">整体的链路采集来看，整体的采集过程相对简单如下所示：</font>

+ 消费端：生成链路数据，
+ 消费端：将链路数据注入到RPC上下文和日志MDC中
+ 消费端：进行RPC调用
+ 服务端：接收链路数据，设置到日志MDC中
+ 提供端：处理业务
+ 提供端：上报链路数据
+ 消费端：上报链路数据



#### 消费端的实现
<font style="color:#080808;background-color:#ffffff;">整个过程相对来说并不复杂，接下来我们先来看消费端生成链路数据的过程，消费端链路数据的处理是在</font>`<font style="color:#080808;background-color:#ffffff;">ObservationSenderFilter</font>`<font style="color:#080808;background-color:#ffffff;">过滤器中，当RPC请求经过过滤器的时候则会调用过滤器的invoke方法，代码如下所示：</font>

```java
@Activate(
        group = CONSUMER,
        order = Integer.MIN_VALUE + 50,
        onClass = "io.micrometer.observation.NoopObservationRegistry")
public class ObservationSenderFilter implements ClusterFilter, BaseFilter.Listener, ScopeModelAware {

    ...

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        ...
        // 上下文对象封装调用信息
        final DubboClientContext senderContext = new DubboClientContext(invoker, invocation);
        // 获取Observation 对象，用于记录和追踪调用过程
        final Observation observation = DubboObservationDocumentation.CLIENT.observation(
                this.clientObservationConvention,
                DefaultDubboClientObservationConvention.getInstance(),
                () -> senderContext,
                observationRegistry);
        //将创建的 Observation 对象放入 invocation 中，并启动观察
        invocation.put(Observation.class, observation.start());
        //使用 observation 的 scoped 方法执行 invoker 的 invoke 方法，并返回结果。
        //scoped 方法确保在调用过程中正确记录和追踪
        return observation.scoped(() -> invoker.invoke(invocation));
    }
    ...
}
```

<font style="color:#080808;background-color:#ffffff;">可以看到在消费端比较核心的逻辑是通过获取Observation观测对象，然后调用其start方法和scoped，其中Observation类型是Micrometer针对可观测逻辑的高度抽象，</font><font style="color:rgb(25, 30, 30);">其背后的主要思想是只需编写一次代码，即可获得多重好处即只需检测一次代码，然后根据处理程序的设置观察，将发生不同的操作，例如创建跨度、指标、日志等。</font><font style="color:#080808;background-color:#ffffff;">Observation API整体的逻辑较多，回到我们链路处理逻辑中，在Observation API调用的时候哪些阶段进行了链路生成呢？</font><font style="color:rgb(25, 30, 30);">在observation对象的创建，默认实现下会创建一个SimpleObservation类型的对象，SimpleObservation 类实现了 Observation 接口，用于管理和记录观察的生命周期和上下文信息，在SimpleObservation执行</font><font style="color:#080808;background-color:#ffffff;">start方法的时候将会调用</font><font style="color:rgb(25, 30, 30);">处理</font>`<font style="color:rgb(25, 30, 30);">Observation</font>`<font style="color:rgb(25, 30, 30);">生命周期的</font><font style="color:#080808;background-color:#ffffff;">ObservationHandler实现，调用过程如下所示：</font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1743375532628-6252b139-953d-4523-9c1c-a16735af6146.png)

<font style="color:#080808;background-color:#ffffff;">其中一个处理器实现类型为</font><font style="color:#080808;background-color:#ffffff;">PropagatingSenderTracingObservationHandler</font>

<font style="color:#080808;background-color:#ffffff;"> 在这个类型的onStart方法中开始创建链路信息然后将其注入到RPC上下文中（用于传递到下游），具体代码如下所示：</font>

```java
@Override
public void onStart(T context) {
    //用于创建发送方 span 
    Span childSpan = createSenderSpan(context);
    //执行注入链路数据到RPC请求中
    this.propagator.inject(childSpan.context(), context.getCarrier(),
                       (carrier, key, value) -> context.getSetter().set(carrier, key, value));
    ...
}
```

<font style="color:#080808;background-color:#ffffff;">首先我们来看一个Span是如何产生的：</font>

<font style="color:#080808;background-color:#ffffff;">PropagatingSenderTracingObservationHandler的createSenderSpan方法</font>

```java
public Span createSenderSpan(T context) {
        //获取已经存在的Span，用于为即将创建的Span关联父Span
        Span parentSpan = getParentSpan(context);
        //创建一个Span构建器，用于构建Span
        Span.Builder builder = getTracer().spanBuilder().kind(Span.Kind.valueOf(context.getKind().name()));
        //设置父Span
        if (parentSpan != null) {
            builder = builder.setParent(parentSpan.context());
        }
        ...
        //构建器启动，创建并启动Span
        return builder.start();
    }
```

<font style="color:#080808;background-color:#ffffff;">可以看到Span的创建是使用Span.Builder来构建的，其中在构建之前会进行父Span属性的设置，接下来我们重点来看下一个Span是如何构建与启动的。</font>

<font style="color:#080808;background-color:#ffffff;">BraveSpanBuilder的start方法：</font>

```java
@Override
public Span start() {
    //span对象的创建
    brave.Span span = span();
    //启动span，为其设置开始时间
    span.start();
    return BraveSpan.fromBrave(span);
}
```

<font style="color:#080808;background-color:#ffffff;"> BraveSpanBuilder的span方法:</font>

```java
private brave.Span span() {
    // 声明一个 brave.Span 类型的变量 span
    brave.Span span;
    
    // 如果 parentContext 不为空，则使用 parentContext 创建一个新的子 span
    if (this.parentContext != null) {
        span = this.tracer.nextSpan(this.parentContext);
    }
    // 否则，创建一个新的 trace
    else {
        span = this.tracer.nextSpan();
    }
    // 设置 span 的名称
    span.name(this.name);
    // 为 span 添加事件
    this.events.forEach(span::annotate);
    // 为 span 添加标签
    this.tags.forEach(span::tag);
    // 设置 span 的错误信息
    span.error(this.error);
    // 设置 span 的类型
    span.kind(this.kind);
    // 设置 span 的远程服务名称
    span.remoteServiceName(this.remoteServiceName);
    // 设置 span 的远程 IP 和端口
    span.remoteIpAndPort(this.ip, this.port);
    // 将 span 赋值给 delegate 变量
    this.delegate = span;
    // 返回创建的 span
    return span;
}
```

<font style="color:#080808;background-color:#ffffff;">可以看到在Brave中一个Span包含了事件（annotate 用于在特定的时间点记录一个事件）、标签（添加键值对形式的元数据）、错误，种类等属性，其中比较重要的链路ID信息则是在链路追踪器的nextSpan方法中进行生成，nextSpan方法如下所示：</font>

```java
public Span nextSpan() {
    //获取当前链路上下文
    TraceContext parent = currentTraceContext.get();
    //当前链路上下文存在则创建子Span，否则创建Trace
    return parent != null ? newChild(parent) : newTrace();
  }
```

<font style="color:#080808;background-color:#ffffff;">Span和Trace其实基本一致，唯一不同的是Trace是链路中第一个位置产生的数据，它没有父链路信息，而Span则是根据父链路信息来产生的链路工作单元，通过Trace可以串联所有Span，在这个nextSpan中先获取当前的TraceContext类型对象，这个对象中包含了跟踪标识符和在进程内和进程外传播的采样数据，其中newChild(parent) 和 newTrace() 的区别在于它们创建的 Span 的上下文不同</font>

<font style="color:#080808;background-color:#ffffff;">newTrace()：  </font>

+ <font style="color:#080808;background-color:#ffffff;">作用：创建一个新的根 Span，没有父 Span。</font>
+ <font style="color:#080808;background-color:#ffffff;">上下文：新创建的 Span 有一个全新的 TraceContext，包括新的 traceId 和 spanId。</font>
+ <font style="color:#080808;background-color:#ffffff;">使用场景：当你希望开始一个全新的追踪（trace）时使用。</font>

<font style="color:#080808;background-color:#ffffff;">newChild(parent)：  </font>

+ <font style="color:#080808;background-color:#ffffff;">作用：创建一个新的子 Span，其父 Span 是传入的 parent。</font>
+ <font style="color:#080808;background-color:#ffffff;">上下文：新创建的 Span 继承了父 Span 的 TraceContext，包括 traceId 和 parentId 等信息。</font>
+ <font style="color:#080808;background-color:#ffffff;">使用场景：当你希望在现有的追踪（trace）中创建一个新的子任务（span）时使用。</font>

<font style="color:#080808;background-color:#ffffff;">了解了发送端链路数据的产生接下来我们来看链路数据是如何注入的，回到PropagatingSenderTracingObservationHandler的onStart方法，如下代码所示：</font>

```java
@Override
public void onStart(T context) {
    //用于创建发送方 span 
    Span childSpan = createSenderSpan(context);
    //执行注入链路数据到RPC请求中
    //Propagator当前配置的为BravePropagator
    // carrier 是一个泛型类型，表示可以携带追踪信息的对象。它可以是 HTTP 请求、消息队列消息等
    this.propagator.inject(childSpan.context(), context.getCarrier(),
                       (carrier, key, value) -> context.getSetter().set(carrier, key, value));
    ...
}
```

<font style="color:#080808;background-color:#ffffff;">在onStart方法中会调用传播器的inject来将刚刚产生的链路数据进行注入，这个注入方法中被封装到传播器中，</font>

传播器的核心作用<font style="color:#080808;background-color:#ffffff;">是在分布式系统中，</font>`<font style="color:#080808;background-color:#ffffff;">Propagator</font>`<font style="color:#080808;background-color:#ffffff;"> 负责将追踪上下文（如 Trace ID、Span ID）通过 HTTP Headers 或 RPC 元数据在服务间传递，确保链路完整性，在Dubbo中实现了如下几种传播器场景：</font>

| <font style="color:#080808;background-color:#ffffff;">传播器类型</font> | <font style="color:#080808;background-color:#ffffff;">协议/格式</font> | <font style="color:#080808;background-color:#ffffff;">主要特点</font> | <font style="color:#080808;background-color:#ffffff;">适用场景</font> |
| --- | --- | --- | --- |
| <font style="color:#080808;background-color:#ffffff;">OTel 传播器</font> | <font style="color:#080808;background-color:#ffffff;">W3C TraceContext、B3、Jaeger 等</font> | <font style="color:#080808;background-color:#ffffff;">遵循 W3C 标准化协议，支持多种主流追踪系统格式，兼容性强</font> | <font style="color:#080808;background-color:#ffffff;">OpenTelemetry 生态下的分布式追踪场景，需跨系统/语言协作时 </font> |
| <font style="color:#080808;background-color:#ffffff;">Brave 传播器</font> | <font style="color:#080808;background-color:#ffffff;">B3 格式 (X-B3-* 头或单头 b3)</font> | <font style="color:#080808;background-color:#ffffff;">专为 Zipkin 设计，默认使用多头部格式，支持单头压缩模式</font> | <font style="color:#080808;background-color:#ffffff;">Zipkin/Brave 客户端集成的场景，需与 Zipkin 服务端无缝对接时 </font> |
| <font style="color:#080808;background-color:#ffffff;">Propagator.NOOP</font> | <font style="color:#080808;background-color:#ffffff;">无</font> | <font style="color:#080808;background-color:#ffffff;">空实现传播逻辑，不注入/提取任何上下文信息</font> | <font style="color:#080808;background-color:#ffffff;">测试环境禁用追踪传播，或明确不需要链路关联的特殊业务场景</font> |


<font style="color:#080808;background-color:#ffffff;">这里我们演示的例子使用的传播器是BravePropagator，在BravePropagator的inject方法实现代码如下所示：</font>

```java
@Override
public <C> void inject(TraceContext traceContext, C carrier, Setter<C> setter) {
    //propagation类型为W3CPropagation
    //injector传递Setter对象获取注入器TraceContext.Injector
    //inject方法负责注入链路数据到载体carrier中
    this.tracing.propagation().injector(setter::set).inject(BraveTraceContext.toBrave(traceContext), carrier);
}
```

<font style="color:#080808;background-color:#ffffff;">其中inject方法一共存在三个参数 ：</font>

| 类型 | 说明 |
| --- | --- |
| <font style="color:#080808;background-color:#ffffff;">traceContext类型为TraceContext（这里是BraveTraceContext）</font> | <font style="color:#080808;background-color:#ffffff;">TraceContext  定义了追踪上下文的基本结构和方法，包含了追踪信息，如 traceId、spanId 等。它用于标识和关联分布式追踪中的各个 Span</font> |
| <font style="color:#080808;background-color:#ffffff;">carrier类型为泛型（这里是RpcInvocation）</font> | <font style="color:#080808;background-color:#ffffff;">泛型类型，表示可以携带追踪信息的对象。它可以是 HTTP 请求、消息队列消息等。  </font> |
| <font style="color:#080808;background-color:#ffffff;">setter类型为Setter</font> | <font style="color:#080808;background-color:#ffffff;">用于将追踪信息注入到 carrier 中</font> |


<font style="color:#080808;background-color:#ffffff;">在消费端执行注入（Inject）逻辑，在发送送请求时，将追踪信息注入到 carrier 中。Micrometer 提供了 Propagator.Setter 接口来设置 carrier 中的追踪信息。  </font>

```java
public interface Setter<C> {
    void set(C carrier, String key, String value);
}
```

<font style="color:#080808;background-color:#ffffff;">同样在提供端也会执行提取（Extract）逻辑： 在接收请求或消息时，需要从 carrier 中提取追踪信息。Micrometer 提供了 Propagator.Getter 接口来获取 carrier 中的追踪信息。  </font>

```java
public interface Getter<C> {
    @Nullable
    String get(C carrier, String key);
}
```

<font style="color:#080808;background-color:#ffffff;"></font>

<font style="color:#080808;background-color:#ffffff;">在这里消费端注入链路数据的整体调用过程可以通过如下栈结构来看（执行过程从下到上）：</font>

```java
// 调用 RpcInvocation 类的 setAttachment 方法设置链路数据
RpcInvocation.setAttachment
...
// 调用 PropagatingSenderTracingObservationHandler 类的 lambda$onStart$0 方法
PropagatingSenderTracingObservationHandler.lambda$onStart$0
...
// 调用 W3CPropagation 类的 lambda$injector$0 方法
W3CPropagation.lambda$injector$0
...
// 调用 BravePropagator 类的 inject 方法
BravePropagator.inject
// 调用 PropagatingSenderTracingObservationHandler 类的 onStart 方法
PropagatingSenderTracingObservationHandler.onStart
...
// 调用 SimpleObservation 类的 notifyOnObservationStarted 方法
SimpleObservation.notifyOnObservationStarted
// 调用 SimpleObservation 类的 start 方法
SimpleObservation.start
// 调用 ObservationSenderFilter 类的 invoke 方法
ObservationSenderFilter.invoke
```

这里注入的链路数据示例如下所示：

<font style="color:#080808;background-color:#ffffff;">key为 </font>`<font style="color:#080808;background-color:#ffffff;">traceparent</font>`<font style="color:#080808;background-color:#ffffff;"> ，值为 00-4bf92f3577b34da6a3ce929d0e0e4736-00f067aa0ba902b7-01</font>

以下是 `traceparent` 的组成部分及其说明，使用表格表示：

| <font style="color:#080808;background-color:#ffffff;">组成部分</font> | <font style="color:#080808;background-color:#ffffff;">说明</font> | <font style="color:#080808;background-color:#ffffff;">示例值</font> |
| --- | --- | --- |
| <font style="color:#080808;background-color:#ffffff;">版本</font> | <font style="color:#080808;background-color:#ffffff;">一个 2 位的十六进制数，表示 </font>`<font style="color:#080808;background-color:#ffffff;">traceparent</font>`<font style="color:#080808;background-color:#ffffff;"> 标头的版本号。</font> | `<font style="color:#080808;background-color:#ffffff;">00</font>` |
| <font style="color:#080808;background-color:#ffffff;">跟踪 ID</font> | <font style="color:#080808;background-color:#ffffff;">一个 32 位的十六进制数，表示整个分布式跟踪的唯一标识符。</font> | `<font style="color:#080808;background-color:#ffffff;">4bf92f3577b34da6a3ce929d0e0e4736</font>` |
| <font style="color:#080808;background-color:#ffffff;">父 ID</font> | <font style="color:#080808;background-color:#ffffff;">一个 16 位的十六进制数，表示当前请求的父请求的标识符。</font> | `<font style="color:#080808;background-color:#ffffff;">00f067aa0ba902b7</font>` |
| <font style="color:#080808;background-color:#ffffff;">跟踪标志</font> | <font style="color:#080808;background-color:#ffffff;">一个 2 位的十六进制数，表示跟踪标志（例如，采样标志）。</font> | `<font style="color:#080808;background-color:#ffffff;">01</font>`<font style="color:#080808;background-color:#ffffff;">（表示采样标志）</font> |


前面在observation对象的start方法中介绍了链路信息向下游的传播机制，在消费端发起调用时候还有一个我们比较关注的就日志中是如何展示链路信息的，如下所示：

```java
[03/04/25 08:02:00:456 CST] main  INFO consumer.ConsumerClassLoaderFilter [67edcff8c1fc5e56b3ceb040bf81ea02, b3ceb040bf81ea02]: ConsumerClassLoaderFilter is invoked
```

日志中链路信息的打印依赖日志的MDC（_<font style="color:rgb(217, 48, 37);">Mapped Diagnostic Context</font>_）实现，在日志框架中MDC的实现一般使用<font style="color:#080808;background-color:#ffffff;">ThreadLocal来存储，是</font><font style="color:rgb(51, 51, 51);">线程独立的，如下是当前示例中traceid存储在MDC时候的调用Debug截图，可以看到此时存储了一个key为traceId，值为67edd275ef1c8a5e763cfeac4b08610a的数据到</font><font style="color:#080808;background-color:#ffffff;">ThreadLocal中</font>

![](https://cdn.nlark.com/yuque/0/2025/png/35284387/1743639306026-07212aea-3ea9-4647-96e7-fca739f02e4f.png)

那存储到日志MDC中的数据又是如何读取后打印到日志控制台呢？这就又要依赖日志框架的MDC日志读取方式通过<font style="color:rgb(51, 51, 51);">%X{key} 指定输出MDC Map中的key的值，回头最初我们示例中的日志配置，通过配置%X{traceId},%X{spanId}来读取链路信息到日志中，如下所示：</font>

```yaml
# tracing信息输出到logging
logging:
  pattern:
    level: '%5p [${spring.application.name:},%X{traceId},%X{spanId}]'
```

这里需要注意的是MDC的实现使用了<font style="color:#080808;background-color:#ffffff;">ThreadLocal，为了避免ThreadLocal内存泄漏的风险，在当前调用完毕之后就要清理掉ThreadLocal中的数据，在</font>消费端一个完整的流程是这样的在发起RPC调用之前产生的链路信息需要放入日志MDC（_<font style="color:rgb(217, 48, 37);">Mapped Diagnostic Context</font>_）中，然后发起RPC调用，在RPC调用完成之则需要将MDC中的链路信息清理掉，关于这一块的调用逻辑则在observation.scoped方法中通过<font style="color:#080808;background-color:#ffffff;">openScope方法来将链路信息放入到MDC，在RPC调用完成之后则调用close方法从MDC中清除链路数据，具体细节则不做展开。</font>

最后来看消费端链路信息的上报，在消费端过滤器ObservationSenderFilter中在invoke方法中发起RPC调用，调用完成之后则根据调用结果来调用onResponse或者onError方法，如下所示

```java
@Activate(
    group = {"consumer"},
    order = -**********,
    onClass = {"io.micrometer.observation.NoopObservationRegistry"}
)
public class ObservationSenderFilter implements ClusterFilter, BaseFilter.Listener, ScopeModelAware {
    ...
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
             ...
             observation.start();
             ...
            return (Result)observation.scoped(() -> {
                return invoker.invoke(invocation);
            });
        }
    }

    public void onResponse(Result appResponse, Invoker<?> invoker, Invocation invocation) {
        ...
        if (appResponse != null && appResponse.hasException()) {
            //记录这个错误
           observation.error(appResponse.getException());
        }
        //停止观测、上报数据
        observation.stop();
        ...
    }

    public void onError(Throwable t, Invoker<?> invoker, Invocation invocation) {
        ...
        //记录这个错误
        observation.error(t);
        //停止观测、上报数据
        observation.stop();
        ...
    }

```

<font style="color:#080808;background-color:#ffffff;">在</font>`<font style="color:#080808;background-color:#ffffff;">observation.error(t);</font>`<font style="color:#080808;background-color:#ffffff;">方法中将会记录异常数据，然后在</font>`<font style="color:#080808;background-color:#ffffff;">observation.stop()</font>`<font style="color:#080808;background-color:#ffffff;"> 中停止观测，上报数据，在</font>`<font style="color:#080808;background-color:#ffffff;">stop</font>`<font style="color:#080808;background-color:#ffffff;">方法中将会调用</font>`<font style="color:#080808;background-color:#ffffff;">PropagatingSenderTracingObservationHandler</font>`<font style="color:#080808;background-color:#ffffff;">的</font>`<font style="color:#080808;background-color:#ffffff;">onStop</font>`<font style="color:#080808;background-color:#ffffff;">方法来停止当前Span，代码如下所示：</font>

```java
@Override
    public void onStop(T context) {
        //从上下文中获取当前的 Span 对象
        Span span = getRequiredSpan(context);
        ...
        //结束 Span
        span.end();
    }
```

在span的end方法中将调用到<font style="color:#080808;background-color:#ffffff;">ZipkinSpanHandler的end方法来触发上报逻辑，代码如下所示：</font>

```java
public boolean end(TraceContext context, MutableSpan span, SpanHandler.Cause cause) {
        ...
        this.spanReporter.report(span);
        return true;
        ...
}
```

<font style="color:#080808;background-color:#ffffff;">在report方法中并不会进行数据上报，最终会把上报的数据存放在一个队列中，然后在异步线程中实现批量上报，最终放入队列的逻辑则在</font>

<font style="color:#080808;background-color:#ffffff;">AsyncReporter$BoundedAsyncReporter的report方法中，代码如下所示：</font>

```java
@Override 
public void report(S next) {
      ...
        //启动刷新上报数据线程
      if (started.compareAndSet(false, true)) startFlusherThread();
     ...

      //将span放入pending队列 
      // enqueue now and filter our when we drain
      if (closed.get() || !pending.offer(next)) {
        //放入队列失败则记录丢弃的梳理
        metrics.incrementSpansDropped(1);
      }
 }

```

<font style="color:#080808;background-color:#ffffff;">可以看到在BoundedQueue的report方法中并不会直接调用zipkin的上报接口进行数据上报，而是先将span放入一个pending队列中，pending队列类型为BoundedQueue是zipkin自己实现的队列，表示一个可以按计数或大小限制的多生产者、多消费者队列，在AsyncReporter$Flusher.run异步上报数据的方法中将会调用AsyncReporter$BoundedAsyncReporter的flush方法来上报数据，具体代码如下所示：</font>

```java
void flush(BufferNextMessage<S> bundler) {
      //将pending中的span存放到bundler中
      pending.drainTo(bundler, bundler.remainingNanos());
      ...
      //创建nextMessage对象用于上报数据
      final ArrayList<byte[]> nextMessage = new ArrayList<byte[]>(bundler.count());
      //对bundler中的数据进行序列话编码，然后将编码之后的数据存放在nextMessage对象中
      bundler.drain(new SpanWithSizeConsumer<S>() {
          @Override 
          public boolean offer(S next, int nextSizeInBytes) {
              //序列化span数据
              nextMessage.add(encoder.encode(next)); 
              ...
              return true;
        }
      });
     ...
     //调用发送器来发送数据，当前发送器类型为URLConnectionSender
     sender.send(nextMessage);
     ...
    }
```

<font style="color:#080808;background-color:#ffffff;">可以看到在异步任务中先执行span数据的编码，然后将其放入一个批量的集合nextMessage中，最后调用发送器的send发送方法来将数据上报到zipkin</font>

通过Http请求上报的地址为：[http://localhost:9411/api/v2/spans](http://localhost:9411/api/v2/spans)

<font style="color:#080808;background-color:#ffffff;">上报时编码前的数据如下：</font>

```json
{
  "traceId" : "67f46ebdd8c66186f276c0f831b2b7b8",
  "id" : "f276c0f831b2b7b8",
  "kind" : "CLIENT",
  "name" : "org.apache.dubbo.springboot.demo.demoservice/sayhello",
  "timestamp" : 1744072381220262,
  "duration" : 67861919,
  "localEndpoint" : {
    "serviceName" : "dubbo-springboot-demo-consumer",
    "ipv4" : "**************"
  },
  "tags" : {
    "error" : "...",
    "net.peer.name" : "**************",
    "net.peer.port" : "20880",
    "rpc.method" : "sayHello",
    "rpc.service" : "org.apache.dubbo.springboot.demo.DemoService",
    "rpc.system" : "apache_dubbo"
  }
}
```

#### <font style="color:#080808;background-color:#ffffff;"> 提供端的实现</font>
提供端在处理链路的时候整个生命周期与消费端请求的时候并没有什么大的区别，提供端同样是在过滤器中进行链路逻辑的处理，在过滤器的invoke方法中创建服务端的上下文DubboServerContext类型对象，然后调用start方法启动observation，通过scoped方法来简化上下文管理 执行清理逻辑，其中提供端过滤器中的核心链路追踪代码如下ObservationReceiverFilter的invoke方法所示：

```java
@Activate(
    group = {"provider"},
    order = -**********,
    onClass = {"io.micrometer.observation.NoopObservationRegistry"}
)
public class ObservationReceiverFilter implements Filter, BaseFilter.Listener, ScopeModelAware {
    ...

    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        ...
        //创建 DubboServerContext，封装当前请求的上下文信息
        DubboServerContext receiverContext = new DubboServerContext(invoker, invocation);
        //创建 Observation 对象，用于记录当前请求的链路追踪信息
        Observation observation = DubboObservationDocumentation.SERVER.observation(this.serverObservationConvention, 
                                                                                   DefaultDubboServerObservationConvention.getInstance(),
                                                                                   () -> {
                return receiverContext;
        }, this.observationRegistry);
        // 启动 Observation，并将其存储到 invocation 中
        invocation.put(Observation.class, observation.start());
        //在 Observation 的作用域内执行实际的调用逻辑
        return (Result)observation.scoped(() -> {
                return invoker.invoke(invocation);
        });
    }

    public void onResponse(Result appResponse, Invoker<?> invoker, Invocation invocation) {
        ...
        if (appResponse != null && appResponse.hasException()) {
           observation.error(appResponse.getException());
        }
        observation.stop();
       
    }

    public void onError(Throwable t, Invoker<?> invoker, Invocation invocation) {
        ...
        observation.error(t);
        observation.stop();
    }
    ...
}
```

可以看到提供端与消费端执行链路观测的时候整个生命周期是一致的，有所区别的是提供端的链路信息会先从RPC请求中获取链路信息，然后再进行处理，重点来看<font style="color:#080808;background-color:#ffffff;">PropagatingReceiverTracingObservationHandler类型的onStart方法，代码如下所示：</font>

```java
public void onStart(T context) {
    //propagator类型为BravePropagator
    //carrier类型为DecodeableRpcInvocation
    //getter的get方法用来从carrier中提取数据
    Span.Builder extractedSpan = this.propagator.extract(context.getCarrier(),
                                                     (carrier, key) -> context.getGetter().get(carrier, key));
    ...
    //启动后的 Span 设置到追踪上下文中
    getTracingContext(context).setSpan(customizeExtractedSpan(context, extractedSpan).start());

}
```

<font style="color:#080808;background-color:#ffffff;">在提供端接收请求处理链路的逻辑时正好与消费端发送请求时相反，在提供端对传递过来的链路进行提取然后放入追踪上下文中供后续使用。</font>

