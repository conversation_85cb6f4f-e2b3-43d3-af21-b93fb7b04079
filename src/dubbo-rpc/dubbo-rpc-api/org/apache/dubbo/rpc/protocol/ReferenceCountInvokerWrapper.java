/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.dubbo.rpc.protocol;

import org.apache.dubbo.common.URL;
import org.apache.dubbo.common.config.ConfigurationUtils;
import org.apache.dubbo.common.constants.LoggerCodeConstants;
import org.apache.dubbo.common.logger.ErrorTypeAwareLogger;
import org.apache.dubbo.common.logger.LoggerFactory;
import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.Result;
import org.apache.dubbo.rpc.RpcException;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

public class ReferenceCountInvokerWrapper<T> implements Invoker<T> {
    private final ErrorTypeAwareLogger logger =
            LoggerFactory.getErrorTypeAwareLogger(ReferenceCountInvokerWrapper.class);
    private final Invoker<T> invoker;

    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    private final AtomicBoolean destroyed = new AtomicBoolean(false);

    public ReferenceCountInvokerWrapper(Invoker<T> invoker) {
        this.invoker = invoker;
    }

    @Override
    public URL getUrl() {
        return invoker.getUrl();
    }

    @Override
    public boolean isAvailable() {
        return !destroyed.get() && invoker.isAvailable();
    }

    @Override
    public void destroy() {
        try {
            int timeout =
                    ConfigurationUtils.getServerShutdownTimeout(invoker.getUrl().getScopeModel());
            boolean locked = lock.writeLock().tryLock(timeout, TimeUnit.MILLISECONDS);
            if (!locked) {
                logger.warn(
                        LoggerCodeConstants.PROTOCOL_CLOSED_SERVER,
                        "",
                        "",
                        "Failed to wait for invocation end in " + timeout + "ms.");
            }
            destroyed.set(true);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            try {
                lock.writeLock().unlock();
            } catch (IllegalMonitorStateException ignore) {
                // ignore if lock failed, maybe in a long invoke
            } catch (Throwable t) {
                logger.warn(
                        LoggerCodeConstants.PROTOCOL_CLOSED_SERVER,
                        "",
                        "",
                        "Unexpected error occurred when releasing write lock, cause: " + t.getMessage(),
                        t);
            }
        }
        invoker.destroy();
    }

    @Override
    public Class<T> getInterface() {
        return invoker.getInterface();
    }

    @Override
    public Result invoke(Invocation invocation) throws RpcException {
        try {
            lock.readLock().lock();
            if (destroyed.get()) {
                logger.warn(
                        LoggerCodeConstants.PROTOCOL_CLOSED_SERVER,
                        "",
                        "",
                        "Remote invoker has been destroyed, and unable to invoke anymore.");
                throw new RpcException("This invoker has been destroyed!");
            }
            return invoker.invoke(invocation);
        } finally {
            lock.readLock().unlock();
        }
    }

    public Invoker<T> getInvoker() {
        return invoker;
    }
}
