/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.dubbo.rpc.protocol.tri.rest.openapi;

public final class Constants {

    public static final String VERSION_30 = "3.0.1";
    public static final String VERSION_31 = "3.1.0";

    public static final String ALL_GROUP = "all";
    public static final String DEFAULT_GROUP = "default";
    public static final String GLOBAL_GROUP = "";

    public static final String X_API_GROUP = "x-api-group";
    public static final String X_API_VERSION = "x-api-version";

    public static final String X_JAVA_CLASS = "x-java-class";
    public static final String X_JAVA_METHOD = "x-java-method";
    public static final String X_JAVA_METHOD_DESCRIPTOR = "x-java-method-descriptor";

    public static final String DUBBO_DEFAULT_SERVER = "Dubbo Default Server";
    public static final String REFERER = "referer";

    private Constants() {}
}
