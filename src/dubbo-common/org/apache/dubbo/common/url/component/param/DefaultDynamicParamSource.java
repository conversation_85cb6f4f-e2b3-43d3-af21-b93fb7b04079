/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.dubbo.common.url.component.param;

import org.apache.dubbo.common.constants.CommonConstants;

import java.util.List;

public class DefaultDynamicParamSource implements DynamicParamSource {
    @Override
    public void init(List<String> keys, List<ParamValue> values) {
        keys.add(CommonConstants.VERSION_KEY);
        values.add(new DynamicValues(null));

        keys.add(CommonConstants.SIDE_KEY);
        values.add(new FixedParamValue(CommonConstants.CONSUMER_SIDE, CommonConstants.PROVIDER_SIDE));

        keys.add(CommonConstants.INTERFACE_KEY);
        values.add(new DynamicValues(null));

        keys.add(CommonConstants.PID_KEY);
        values.add(new DynamicValues(null));

        keys.add(CommonConstants.THREADPOOL_KEY);
        values.add(new DynamicValues(null));

        keys.add(CommonConstants.GROUP_KEY);
        values.add(new DynamicValues(null));

        keys.add(CommonConstants.VERSION_KEY);
        values.add(new DynamicValues(null));

        keys.add(CommonConstants.METADATA_KEY);
        values.add(new DynamicValues(null));

        keys.add(CommonConstants.APPLICATION_KEY);
        values.add(new DynamicValues(null));

        keys.add(CommonConstants.DUBBO_VERSION_KEY);
        values.add(new DynamicValues(null));

        keys.add(CommonConstants.RELEASE_KEY);
        values.add(new DynamicValues(null));

        keys.add(CommonConstants.PATH_KEY);
        values.add(new DynamicValues(null));

        keys.add(CommonConstants.ANYHOST_KEY);
        values.add(new DynamicValues(null));
    }
}
